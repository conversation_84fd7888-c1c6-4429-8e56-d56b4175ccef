import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import size from './modules/size'

// default router permission control
import permission from './modules/async-router'

import getters from './getters'

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    app,
    user,
    size,
    permission
  },
  state: {},
  mutations: {},
  actions: {},
  getters
})
