<template>
  <div
    class="expand-table-container"
    @wheel.stop="handleWheel"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <VxeTable
      ref="vxeTableRef"
      :isShowTableHeader="false"
      :tableKey="tableKey"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="false"
      :rowConfig="{ isHover: false }"
      height="240px"
      size="small"
      @refresh="getList"
    ></VxeTable>
    <OptCmdDetail v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import OptCmdDetail from '../../opt-command/modules/OptCmdDetail.vue'
  import { getRunmdById } from '../services'
  import { getOperateCmdPage } from '../../opt-command/services'

  export default {
    name: 'ExpandTable',
    components: { VxeTable, OptCmdDetail },
    props: ['row'],
    data() {
      return {
        showForm: false,
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '日期',
            field: 'operateDate',
            minWidth: 120,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                if (row.operateDate) {
                  // 处理日期格式，将 ISO 格式转换为 YYYY-MM-DD HH:mm:ss
                  const date = new Date(row.operateDate)
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    }).replace(/\//g, '-')
                  }
                }
                return '-'
              }
            }
          },
          { title: '工程名称', field: 'projectName', minWidth: 120, showOverflow: 'tooltip' },
          { title: '操作人', field: 'operateName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '监护人', field: 'guardianName', minWidth: 100, showOverflow: 'tooltip' },
          {
            title: '接收状态',
            field: 'recStatusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                return row.recStatusCode === 1 ? '已接收' : '未接收'
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>详情</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {
      // 确保表格渲染完成后设置正确的滚动行为
      this.$nextTick(() => {
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          // 确保滚动容器有正确的样式
          tableBodyWrapper.style.overflowY = 'auto'
          tableBodyWrapper.style.overflowX = 'hidden'
          tableBodyWrapper.style.height = '240px'
          tableBodyWrapper.style.maxHeight = '240px'

          // 添加滚动条点击事件监听
          tableBodyWrapper.addEventListener('click', (e) => {
            // 确保点击事件不会被阻止
            e.stopPropagation()
          })
        }
      })
    },
    methods: {
      getList() {
        this.loading = true

        // 同时调用两个接口
        Promise.all([
          getRunmdById({ runCmdId: this.row.runCmdId }),
          getOperateCmdPage({ cmdCode: this.row.cmdCode, pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER })
        ]).then(([runmdResponse, operateResponse]) => {
          let projectOpInfoList = []
          let operateCmdList = []

          // 获取工程操作信息列表
          if (runmdResponse.code === 200 && runmdResponse.data && runmdResponse.data.projectOpInfoList) {
            projectOpInfoList = runmdResponse.data.projectOpInfoList
          }

          // 获取操作命令分页数据并倒序排列
          if (operateResponse.code === 200 && operateResponse.data && operateResponse.data.data) {
            operateCmdList = operateResponse.data.data.reverse() // 倒序排列
          }

          // 将operateCmdId赋值给projectOpInfoList中对应的项
          if (projectOpInfoList.length > 0 && operateCmdList.length > 0) {
            projectOpInfoList.forEach((item, index) => {
              if (operateCmdList[index] && operateCmdList[index].operateCmdId) {
                item.operateCmdId = operateCmdList[index].operateCmdId
              }
            })
          }

          this.list = projectOpInfoList
          this.loading = false
        }).catch(() => {
          this.list = []
          this.loading = false
        })
      },

      // 处理滚轮事件，阻止事件冒泡
      handleWheel(event) {
        // 阻止事件冒泡到父级表格
        event.stopPropagation()
        event.preventDefault()

        // 获取表格的滚动容器
        const tableBodyWrapper = this.$el.querySelector('.vxe-table--body-wrapper')
        if (tableBodyWrapper) {
          const scrollTop = tableBodyWrapper.scrollTop
          const scrollHeight = tableBodyWrapper.scrollHeight
          const clientHeight = tableBodyWrapper.clientHeight

          // 如果内容高度小于等于容器高度，不需要滚动
          if (scrollHeight <= clientHeight) {
            return
          }

          // 计算滚动距离，调整滚动速度
          const deltaY = event.deltaY * 0.5 // 降低滚动速度，提供更好的用户体验
          const newScrollTop = scrollTop + deltaY

          // 限制滚动范围
          if (newScrollTop >= 0 && newScrollTop <= scrollHeight - clientHeight) {
            tableBodyWrapper.scrollTop = newScrollTop
          } else if (newScrollTop < 0) {
            tableBodyWrapper.scrollTop = 0
          } else {
            tableBodyWrapper.scrollTop = scrollHeight - clientHeight
          }
        }
      },

      // 鼠标进入时添加焦点样式
      handleMouseEnter() {
        this.$el.classList.add('expand-table-focused')
      },

      // 鼠标离开时移除焦点样式
      handleMouseLeave() {
        this.$el.classList.remove('expand-table-focused')
      },

      handleDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      onOperationComplete() {
        this.isShowModal = false
      },
    },
  }
</script>

<style lang="less" scoped>
  .expand-table-container {
    height: 240px;
    position: relative;
    overflow: hidden;
    border: 1px solid #e8e8e8;
    border-radius: 4px;

    // 焦点状态样式
    &.expand-table-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 0px !important;
    }
  }

  // 确保表格主体有正确的滚动行为
  ::v-deep .vxe-table--render-default .vxe-table--body-wrapper {
    height: 240px !important;
    max-height: 240px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    position: relative !important;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
      background: transparent;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
      margin: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      cursor: pointer;
      min-height: 20px;

      &:hover {
        background: #a8a8a8;
      }

      &:active {
        background: #787878;
      }
    }

    // 确保滚动条按钮可以点击
    &::-webkit-scrollbar-button {
      display: block;
      height: 12px;
      background: #e1e1e1;
      cursor: pointer;

      &:hover {
        background: #d1d1d1;
      }

      &:active {
        background: #c1c1c1;
      }
    }

    // 上箭头
    &::-webkit-scrollbar-button:vertical:start:decrement {
      background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8'%3E%3Cpath d='M4 2L1 6h6z' fill='%23666'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
    }

    // 下箭头
    &::-webkit-scrollbar-button:vertical:end:increment {
      background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8'%3E%3Cpath d='M4 6L1 2h6z' fill='%23666'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
    }
  }

  // 确保表格内容区域正确显示
  ::v-deep .vxe-table--render-default .vxe-table--body {
    position: relative !important;
  }

  // 添加展开表格的容器样式
  ::v-deep .vxe-table--expanded-wrapper {
    .vxe-table {
      height: 240px !important;
    }
  }

  // 确保表格行正常显示
  ::v-deep .vxe-table--render-default .vxe-body--row {
    position: relative;
  }

  // 修复可能的z-index问题
  ::v-deep .vxe-table--render-default {
    position: relative;
    z-index: 1;
  }
</style>
