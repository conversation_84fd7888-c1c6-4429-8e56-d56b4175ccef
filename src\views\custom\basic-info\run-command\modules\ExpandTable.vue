<template>
  <div style="height: 100%">
    <VxeTable
      ref="vxeTableRef"
      :isShowTableHeader="false"
      :tableKey="tableKey"
      :columns="columns"
      :tableData="list"
      :loading="loading"
      :isShowSize="false"
      :isShowColumns="false"
      :isShowFull="false"
      :autoHeight="true"
      :rowConfig="{ isHover: false }"
      min-height="82px"
      max-height="280px"
      size="small"
      @refresh="getList"
    ></VxeTable>
    <OptCmdDetail v-if="showForm" ref="formRef" @ok="onOperationComplete" @close="showForm = false" />
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import OptCmdDetail from '../../opt-command/modules/OptCmdDetail.vue'
  import { getRunmdById } from '../services'
  import { getOperateCmdPage } from '../../opt-command/services'

  export default {
    name: 'ExpandTable',
    components: { VxeTable, OptCmdDetail },
    props: ['row'],
    data() {
      return {
        showForm: false,
        tableKey: 1,
        loading: true,
        list: [],
        selectIds: [],
        columns: [
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1
              },
            },
          },
          {
            title: '日期',
            field: 'operateDate',
            minWidth: 120,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                if (row.operateDate) {
                  // 处理日期格式，将 ISO 格式转换为 YYYY-MM-DD HH:mm:ss
                  const date = new Date(row.operateDate)
                  if (!isNaN(date.getTime())) {
                    return date.toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    }).replace(/\//g, '-')
                  }
                }
                return '-'
              }
            }
          },
          { title: '工程名称', field: 'projectName', minWidth: 120, showOverflow: 'tooltip' },
          { title: '操作人', field: 'operateName', minWidth: 100, showOverflow: 'tooltip' },
          { title: '监护人', field: 'guardianName', minWidth: 100, showOverflow: 'tooltip' },
          {
            title: '接收状态',
            field: 'recStatusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                return row.recStatusCode === 1 ? '已接收' : '未接收'
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 80,
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>详情</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
    },
    mounted() {},
    methods: {
      getList() {
        this.loading = true

        // 同时调用两个接口
        Promise.all([
          getRunmdById({ runCmdId: this.row.runCmdId }),
          getOperateCmdPage({ cmdCode: this.row.cmdCode, pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER })
        ]).then(([runmdResponse, operateResponse]) => {
          let projectOpInfoList = []
          let operateCmdList = []

          // 获取工程操作信息列表
          if (runmdResponse.code === 200 && runmdResponse.data && runmdResponse.data.projectOpInfoList) {
            projectOpInfoList = runmdResponse.data.projectOpInfoList
          }

          // 获取操作命令分页数据并倒序排列
          if (operateResponse.code === 200 && operateResponse.data && operateResponse.data.data) {
            operateCmdList = operateResponse.data.data.reverse() // 倒序排列
          }

          // 将operateCmdId赋值给projectOpInfoList中对应的项
          if (projectOpInfoList.length > 0 && operateCmdList.length > 0) {
            projectOpInfoList.forEach((item, index) => {
              if (operateCmdList[index] && operateCmdList[index].operateCmdId) {
                item.operateCmdId = operateCmdList[index].operateCmdId
              }
            })
          }

          this.list = projectOpInfoList
          this.loading = false
        }).catch(() => {
          this.list = []
          this.loading = false
        })
      },

      handleDetail(record) {
        this.showForm = true
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      onOperationComplete() {
        this.isShowModal = false
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .vxe-table-box-content {
    .sortable-column-demo.vxe-grid {
      padding-bottom: 0px !important;
      margin-bottom: 10px;
    }
  }

  ::v-deep .vxe-table--render-default .vxe-table--body-wrapper {
    height: auto !important;
    min-height: 102px !important; // 确保最小高度
  }

  // 添加展开表格的容器样式
  ::v-deep .vxe-table--expanded-wrapper {
    .vxe-table {
      min-height: 102px !important;
    }
  }
</style>
