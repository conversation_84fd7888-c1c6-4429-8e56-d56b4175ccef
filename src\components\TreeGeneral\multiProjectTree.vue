<template>
  <div style="height: 100%">
    <a-input placeholder="请输入" @change="onChange" allowClear />
    <div :class="hasTab ? 'tab-tree-panel-box' : 'tree-panel-tree-box'">
      <div class="loading" v-if="loading">
        <a-spin />
      </div>
      <AntTree
        v-if="treeData.length > 0"
        :tree-data="treeData"
        :default-expanded-keys="expandedKeys"
        :expanded-keys="expandedKeys"
        :auto-expand-parent="autoExpandParent"
        :disabled="isDisabled"
        showIcon
        showLine
        @check="handleCheck"
        @expand="onExpand"
        checkable
        :checkedKeys="checkedKeys"
      >
        <a-icon slot="switcherIcon" type="caret-down" />

        <SvgIcon slot="HP018" iconClass="圩区" class="depIcon" />
        <SvgIcon slot="project" iconClass="工程" class="depIcon" />
        <SvgIcon slot="device" iconClass="设备" class="depIcon" />

        <!-- <a-icon slot="org" type="org" class="depIcon" />
        <a-icon slot="company" type="company" class="depIcon" />
        <a-icon slot="dept" type="dept" class="depIcon" />
        <a-icon slot="user" type="user" class="depIcon" /> -->
        <template slot="title" slot-scope="{ title }">
          <span v-if="title.indexOf(searchValue) > -1">
            {{ title.substr(0, title.indexOf(searchValue)) }}
            <span style="color: #f50">{{ searchValue }}</span>
            {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
          </span>
          <span v-else>{{ title }}</span>
        </template>
      </AntTree>
    </div>
  </div>
</template>
<script lang="jsx">
  /**
   * 业务场景
   * 1. 多选
   * 2. 只选叶子节点(节点isLeaf为true)
   * 3. 默认选中树的第一个叶子节点
   */
  import AntTree from 'ant-design-vue/es/tree'
  import getFlatTreeMap from '@/utils/getMapFlatTree.js'

  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }

  function handleTreeData(data) {
    data.forEach(item => {
      item['disabled'] = item.isLeaf
      // if (!this.isDisabled) {
      //   item['disabled'] = item.isLeaf
      // }
      if (item.children) {
        handleTreeData(item.children)
      }
    })
    return data
  }
  function getIcon(ele) {
    if (ele.type == 'HP018') {
      return 'HP018'
    } else if (ele.type == 'PROJECT') {
      return 'project'
    } else {
      return 'device'
    }
  }
  function resetDataSource(data, replaceFields) {
    function dealData(arr) {
      arr.forEach((ele, i) => {
        arr[i] = {
          ...ele,
          key: ele?.[replaceFields.key],
          title: ele?.[replaceFields.title],
          children: ele?.[replaceFields.children],
          slots: { icon: getIcon(ele) },
          // slots: { icon: ele.type },
          scopedSlots: { title: 'title' },
        }

        dealData(ele?.[replaceFields.children] || [])
      })
    }
    dealData(data)

    return data
  }

  // 获取第一个leaf
  function getFirstLeaf(data) {
    let obj
    function dealArr(arr) {
      arr.forEach(ele => {
        if (!obj) {
          if (ele.isLeaf) {
            obj = ele
          } else {
            dealArr(ele.children || [])
          }
        }
      })
    }

    dealArr(data)
    return obj
  }

  export default {
    name: 'MultiTree',
    components: {
      AntTree,
    },
    props: {
      treeDisabled: {
        type: Boolean,
        default: false,
      },
      treeOptions: {
        type: Object,
        required: true,
      },
      isLeafDisabled: {
        type: Boolean,
      },
      defaultExpandedKeys: {
        type: Array,
        default: () => [],
      },
      hasTab: {
        type: Boolean,
        default: false,
      },
      isCheckFirstLeaf: {
        type: Boolean,
        default: true,
      },
      currentKeys: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        loading: false,
        treeData: [],
        isDisabled: this.treeDisabled,
        expandedKeys: this.defaultExpandedKeys,
        key: this.treeOptions.replaceFields.key,
        leafNodes: [],
        searchValue: '',
        autoExpandParent: true,
        dataList: [],
        checkedKeys: this.currentKeys,
        flatTree: [],
      }
    },
    watch: {
      treeOptions: {
        handler(newVal) {
          this.getDataSource(undefined, 'created')
        },
        deep: true,
        immediate: true,
      },
      currentKeys: {
        handler(newVal) {
          this.checkedKeys = newVal
        },
        deep: true,
      },
    },
    filters: {},
    created() {},
    methods: {
      getExpandedKeys(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }

        nodes.forEach(node => {
          this.leafNodes.push(node.key)
          return this.getExpandedKeys(node.children)
        })
      },
      generateList(data) {
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          const key = node.key
          const title = node.title
          this.dataList.push({ key, title })
          if (node.children) {
            this.generateList(node.children)
          }
        }
      },
      // 筛选节点
      onChange(e) {
        const value = e.target.value
        const expandedKeys = this.dataList
          .map(item => {
            if (item.title.indexOf(value) > -1) {
              return getParentKey(item.key, this.treeData)
            }
            return null
          })
          .filter((item, i, self) => item && self.indexOf(item) === i)

        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        })
      },
      // 获取树
      getDataSource(value, type) {
        this.loading = true
        if (!this.treeOptions?.getDataApi) {
          this.loading = false
          this.treeData = resetDataSource(this.treeOptions.dataSource, this.treeOptions.replaceFields)
          // 设置默认选中第一个leaf
          // if (this.isCheckFirstLeaf && this.checkedKeys.length == 0) {
          //   this.checkedKeys = [getFirstLeaf(this.treeData).key]
          // }
          this.flatTree = getFlatTreeMap(this.treeData, 'key')
          this.generateList(this.treeData)

          this.getExpandedKeys(this.treeData)
          Object.assign(this, {
            expandedKeys: this.leafNodes,
            searchValue: value,
            autoExpandParent: true,
          })
          this.leafNodes = []
          if (type === 'created') {
            this.$emit(
              'onTreeMounted',
              this.checkedKeys,
              this.checkedKeys.map(el => this.flatTree[el]),
            )
          }
        } else {
          const searchInfo = { keywords: value }
          this.treeOptions
            .getDataApi(searchInfo)
            .then(response => {
              this.loading = false
              if (this.isLeafDisabled) {
                this.treeData = handleTreeData(response?.data || [])
              }

              this.treeData = resetDataSource(response?.data || [], this.treeOptions.replaceFields)
              // 设置默认选中第一个leaf
              // if (this.isCheckFirstLeaf) {
              //   this.checkedKeys = [getFirstLeaf(this.treeData).key]
              // }
              this.flatTree = getFlatTreeMap(this.treeData, 'key')

              this.generateList(this.treeData)

              this.getExpandedKeys(response.data)
              Object.assign(this, {
                expandedKeys: this.leafNodes,
                searchValue: value,
                autoExpandParent: true,
              })
              this.leafNodes = []
              if (type === 'created') {
                this.$emit(
                  'onTreeMounted',
                  this.checkedKeys,
                  this.checkedKeys.map(el => this.flatTree[el]),
                )
              }
            })
            .catch(res => {
              console.log('error', res)
            })
        }
      },
      // 节点选中事件,
      handleCheck(keys, event) {
        this.checkedKeys = keys.filter(el => {
          let obj = this.flatTree[el]
          return obj.isLeaf
        })

        this.$emit(
          'check',
          this.checkedKeys,
          this.checkedKeys.map(el => this.flatTree[el]),
        )
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #aaa;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
    // margin-bottom: 7px;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
    // margin-top: 7px;
  }

  // 去掉叶子前的icon
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .anticon-file {
    display: none;
  }
  // 去掉叶子前line
  ::v-deep
    .ant-tree.ant-tree-show-line
    .ant-tree-child-tree
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }
  ::v-deep
    .ant-tree.ant-tree-show-line
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }

  // 展开箭头
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher .ant-tree-switcher-icon {
    color: #666;
    font-size: 14px;
  }

  .tree-panel-tree-box {
    position: relative;
    .loading {
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      top: 30px;
    }
  }
</style>
