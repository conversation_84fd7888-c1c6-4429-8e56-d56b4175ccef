import Vue from 'vue'
/**
 * @description 超级用户常量
 */
const SUPERUSER = 'superadmin'
/**
 * @description 默认应用类型
 */

console.log('appid', process.env.VUE_APP_APPID)
const APPID = process.env.VUE_APP_APPID || 'base'
/**
 * @description 默认密码
 */
const INITPWD = '123456'

/**
 * @description 性别
 */
const SEX = [
  { dictKey: '1', dictValue: '男' },
  { dictKey: '2', dictValue: '女' }
]
//保存全局状态
let State = new Vue({
  data() {
    return {
      is_phone: false
    }
  }
})
export default {
  SUPERUSER,
  APPID,
  INITPWD,
  SEX,
  State
}
