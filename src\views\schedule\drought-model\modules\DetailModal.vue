<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="800"
    :footer="null"
  >
    <div slot="content" style="height: 100%; display: flex">
      <div class="left" v-if="!!rowData">
        <div style="margin-bottom: 20px">
          <a-button :type="active === 1 ? 'primary' : 'default'" @click="tabClick(1)" style="margin-right: 10px">
            土壤湿度
          </a-button>
          <a-button :type="active === 2 ? 'primary' : 'default'" @click="tabClick(2)">土壤含水率</a-button>
        </div>

        <div class="item">
          <div class="label">预警编码:&nbsp;</div>
          <div class="value">{{ rowData.warnCode }}</div>
        </div>

        <div class="item" v-if="active === 1">
          <div class="label">矢量图层:&nbsp;</div>
          <div class="value">{{ rowData.humidityUrl }}</div>
        </div>
        <div class="item" v-if="active === 2">
          <div class="label">矢量图层:&nbsp;</div>
          <div class="value">{{ rowData.moistureUrl }}</div>
        </div>
        <div class="item">
          <div class="label">预警时间:&nbsp;</div>
          <div class="value">{{ rowData.warnDate }}</div>
        </div>
        <div style="flex: 1">
          <VxeTable
            ref="vxeTableRef"
            size="small"
            :isShowTableHeader="false"
            :columns="columns"
            :tableData="list"
            :tablePage="false"
          ></VxeTable>
        </div>
      </div>

      <div v-if="!!rowData" style="flex: 1; height: 100%; position: relative">
        <!-- loading -->
        <div
          v-if="mapLoading"
          style="
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
          "
        >
          <a-spin />
        </div>
        <Mapbox @onMapMounted="onMapMounted" />

        <MultiPolygon
          v-if="!!mapIns && active === 1"
          :geojson="allGeojson"
          :mapIns="mapIns"
          :colorAndLabelMap="colorAndLabelMap"
          :checkedList="checkedList"
        />
        <MultiPolygon
          v-if="!!mapIns && active != 1"
          :geojson="allGeojson"
          :mapIns="mapIns"
          :colorAndLabelMap="colorAndLabelMap2"
          :checkedList="checkedList"
        />
        <div class="legend" :style="{ width: active === 1 ? '170px' : '150px' }">
          <div style="margin-bottom: 5px; font-weight: bold">{{ active === 1 ? '干旱等级' : '土壤含水率' }}</div>

          <a-checkbox
            :indeterminate="indeterminate"
            :checked="checkAll"
            @change="onCheckAllChange"
            style="margin-bottom: 5px"
          >
            全选
          </a-checkbox>
          <a-checkbox-group v-model="checkedList" @change="onChange">
            <a-checkbox v-for="(el, idx) in list" :key="idx" :value="el.gridcode" style="margin-left: 0">
              <div class="legend-item" :style="{ background: el.color, width: active === 1 ? '110px' : '90px' }">
                {{ active === 1 ? el.labelHumidity : el.water }}
              </div>
            </a-checkbox>
          </a-checkbox-group>
        </div>
      </div>
    </div>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOptions, getValueByKey } from '@/api/common'
  // import { getDrought } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import Mapbox from '@/components/MapBox/index.vue'
  import MapStyle from '@/components/MapBox/MapStyle.vue'
  import VxeTable from '@/components/VxeTable/index.vue'
  import { mapBoundGeo } from '@/utils/mapBounds.js'
  import axios from 'axios'
  import MultiPolygon from './MultiPolygon.vue'
  import initMap from './initMap'

  export default {
    name: 'FormDrawer',
    props: ['tpOptions'],
    components: { AntModal, VxeTable, Mapbox, MapStyle, MultiPolygon },
    data() {
      return {
        indeterminate: false,
        checkAll: true,
        checkedList: [],

        colorAndLabelMap: {
          5: {
            color: '#FF071B',
            labelHumidity: '重旱（0.6-1）',
            humidityVal: '0%-40%',
          },
          4: {
            color: '#FFB62C',
            labelHumidity: '中旱（0.5-0.6）',
            humidityVal: '40%-50%',
          },
          3: {
            color: '#FFFF38',
            labelHumidity: '轻旱（0.4-0.5）',
            humidityVal: '50%-60%',
          },
          2: {
            color: '#00AEFB',
            labelHumidity: '正常（0.2-0.4）',
            humidityVal: '60%-80%',
          },
          1: {
            color: '#70B603',
            labelHumidity: '湿润（0-0.2）',
            humidityVal: '80%-100%',
          },
        },
        colorAndLabelMap2: {
          1: {
            color: '#FF071B',
            labelHumidity: '重旱（0.6-1）',
            humidityVal: '0%-40%',
          },
          2: {
            color: '#FFB62C',
            labelHumidity: '中旱（0.5-0.6）',
            humidityVal: '40%-50%',
          },
          3: {
            color: '#FFFF38',
            labelHumidity: '轻旱（0.4-0.5）',
            humidityVal: '50%-60%',
          },
          4: {
            color: '#00AEFB',
            labelHumidity: '正常（0.2-0.4）',
            humidityVal: '60%-80%',
          },
          5: {
            color: '#70B603',
            labelHumidity: '湿润（0-0.2）',
            humidityVal: '80%-100%',
          },
        },
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '详情',

        active: 1,

        mapIns: null,
        geojson: null,

        rowData: {},
        list: [],
        columns: [],

        mapLoading: false,
        allGeojson: null,
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      onChange(checkedList) {
        this.indeterminate = !!checkedList.length && checkedList.length < this.list.length
        this.checkAll = checkedList.length === this.list.length
      },
      onCheckAllChange(e) {
        Object.assign(this, {
          checkedList: e.target.checked ? this.list.map(el => el.gridcode) : [],
          indeterminate: false,
          checkAll: e.target.checked,
        })
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      handleShow(row) {
        this.open = true
        this.modalLoading = true
        this.modalTitle = '预警详情'
        this.rowData = row
        this.tabClick(1)
      },

      onMapMounted(mapIns) {
        this.mapIns = mapIns
        initMap(mapIns)
      },

      tabClick(tab) {
        this.active = tab
        if (tab === 1) {
          this.columns = [
            { type: 'seq', title: '序号', width: 40 },
            {
              title: '干旱等级',
              width: 120,
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div
                      style={{
                        background: row?.color,
                        padding: '0px 6px',
                        borderRadius: '4px',
                        width: '110px',
                      }}
                    >
                      {row?.labelHumidity}
                    </div>
                  )
                },
              },
            },
            {
              title: '湿度百分比',
              field: 'humidityVal',
              showOverflow: 'tooltip',
            },
            {
              title: '占地面积(亩)',
              field: 'Area',
              width: 100,
              showOverflow: 'tooltip',
            },
            {
              title: '占地百分比',
              field: 'percent',
              showOverflow: 'tooltip',
            },
          ]

          // 土壤湿度
          // http://gis.1.com/geoserver/sthgq_drought/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq_drought:202501_soil_rh_new&maxFeatures=50&outputFormat=application/json&propertyName=gridcode,humidity,Area
          axios
            .get(
              `${process.env.VUE_APP_GEOSERVER_BASE}/sthgq_drought/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=${this.rowData.humidityUrl}&maxFeatures=50&outputFormat=application/json&propertyName=gridcode,humidity,Area`,
            )
            .then(res => {
              this.modalLoading = false
              this.list = res.data.features.map(el => ({
                gridcode: el.properties.gridcode,
                water: el.properties.water,
                Area: (el.properties.Area * 0.0015).toFixed(1),
                percent:
                  +(
                    (el.properties.Area / res.data.features.reduce((acc, cur) => acc + cur.properties.Area, 0)) *
                    100
                  ).toFixed(2) + '%',
                ...this.colorAndLabelMap[el.properties.gridcode],
              }))

              this.checkedList = res.data.features.map(el => el.properties.gridcode)
              this.onChange(this.checkedList)

              this.mapLoading = true
              axios
                .get(
                  `${process.env.VUE_APP_GEOSERVER_BASE}/sthgq_drought/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=${this.rowData.humidityUrl}&maxFeatures=50&outputFormat=application/json`,
                )
                .then(resp => {
                  this.allGeojson = resp.data
                  this.mapLoading = false
                })
            })
        }
        if (tab === 2) {
          this.columns = [
            { type: 'seq', title: '序号', width: 50 },
            {
              title: '土壤含水率',
              minWidth: 80,
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div
                      style={{
                        background: row?.color,
                        padding: '0px 6px',
                        borderRadius: '4px',
                        width: '80px',
                      }}
                    >
                      {row?.water}
                    </div>
                  )
                },
              },
            },
            {
              title: '占地面积(亩)',
              field: 'Area',
              minWidth: 60,
              showOverflow: 'tooltip',
            },
            {
              title: '占地百分比',
              field: 'percent',
              minWidth: 60,
              showOverflow: 'tooltip',
            },
          ]

          // 含水率
          // http://gis.1.com/geoserver/sthgq_drought/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=sthgq_drought:202501_soil_water_new&maxFeatures=50&outputFormat=application/json&propertyName=gridcode,water,Area
          axios
            .get(
              `${process.env.VUE_APP_GEOSERVER_BASE}/sthgq_drought/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=${this.rowData.moistureUrl}&maxFeatures=50&outputFormat=application/json&propertyName=gridcode,water,Area`,
            )
            .then(res => {
              this.modalLoading = false
              this.list = res.data.features.map(el => ({
                gridcode: el.properties.gridcode,
                water: el.properties.water,
                Area: (el.properties.Area * 0.0015).toFixed(1),
                percent:
                  +(
                    (el.properties.Area / res.data.features.reduce((acc, cur) => acc + cur.properties.Area, 0)) *
                    100
                  ).toFixed(2) + '%',
                ...this.colorAndLabelMap2[el.properties.gridcode],
              }))

              this.checkedList = res.data.features.map(el => el.properties.gridcode)
              this.onChange(this.checkedList)

              this.mapLoading = true
              axios
                .get(
                  `${process.env.VUE_APP_GEOSERVER_BASE}/sthgq_drought/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=${this.rowData.moistureUrl}&maxFeatures=50&outputFormat=application/json`,
                )
                .then(resp => {
                  this.allGeojson = resp.data
                  this.mapLoading = false
                })
            })
        }
      },
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
  .left {
    width: 450px;
    padding: 14px;
    display: flex;
    flex-direction: column;

    .item {
      display: flex;
      height: 40px;
      line-height: 30px;
      .label {
        width: 80px;
        color: #4e5969;
        text-align: right;
      }
      .value {
        flex: 1;
        color: #1d2129;
        // 文字溢出隐藏
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .legend {
    position: absolute;
    z-index: 99999999;
    left: 12px;
    bottom: 12px;
    background: rgba(255, 255, 255, 0.8);
    padding: 8px 12px;
    border-radius: 8px 8px 8px 8px;
    .legend-item {
      font-size: 13px;
      padding-left: 8px;
      display: inline-block;
      border-radius: 4px 4px 4px 4px;
      margin-bottom: 6px;
      user-select: none;
    }
  }
</style>
