.basic-info {
  width: 100%;
  height: calc(100% - 20px);
  overflow: hidden;
  overflow: auto;

  .content {
    .title {
      font-weight: 700;
      font-size: 16px;
      padding: 10px 16px;
      display: flex;
      .ant-btn {
        margin-left: auto !important;
      }
    }
    .second-title {
      border-top: 1px solid #e5e6eb;
    }
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      // background: #aaa;
      .label {
        width: 50%;
        text-align: right;
        color: rgba(0, 0, 0, 0.45);
        // background: red;
      }
      .value {
        width: 50%;
        text-align: left;
        color: rgba(0, 0, 0, 0.88);

        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1; //设置行数
        overflow: hidden; //超出隐藏
      }
    }
    .form-value {
      line-height: 28px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; //设置行数
      overflow: hidden; //超出隐藏
    }
    .note {
      padding-left: 60px;
    }
    .location {
      padding-left: 44px;
    }
    .loc {
      padding-left: 100px;
    }
    .area-text {
      width: 96%;
    }
    .multiple-item {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; //设置行数
      overflow: hidden; //超出隐藏
    }
  }
}
::v-deep .ant-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

::v-deep .ant-form-item {
  margin-bottom: 10px;

  .ant-form-item-label > label {
    color: #aaa;
  }
}
