<template>
  <div style="height: 100%">
    <a-input
      placeholder="请输入"
      @change="onChange"
      allowClear
      style="margin-left: 10px; margin-bottom: 10px; width: 200px"
    />
    <div :class="hasTab ? 'tab-tree-panel-box' : 'tree-panel-tree-box'">
      <div class="loading" v-if="loading">
        <a-spin />
      </div>
      <!-- showIcon -->
      <AntTree
        v-if="treeData.length > 0"
        v-show="!loading"
        :tree-data="treeData"
        :default-expanded-keys="expandedKeys"
        :expanded-keys="expandedKeys"
        :auto-expand-parent="autoExpandParent"
        :showIcon="true"
        showLine
        @select="handleNodeClick"
        @expand="onExpand"
        :selectedKeys="selectedKeys"
      >
        <a-icon slot="switcherIcon" type="caret-down" />

        <SvgIcon
          slot="catalogue"
          iconClass="tree-catalogue"
          class="depIcon"
          style="font-size: 18px; margin-bottom: -1px"
        />
        <SvgIcon slot="leaf" iconClass="tree-leaf" class="depIcon" style="font-size: 18px; margin-bottom: -1px" />

        <SvgIcon
          v-for="icon in allIcons"
          :key="icon"
          :slot="icon"
          :iconClass="icon"
          class="depIcon"
          style="font-size: 18px; margin-bottom: -1px"
        />

        <template slot="title" slot-scope="node">
          <div class="container">
            <span v-if="node.title.indexOf(searchValue) > -1">
              {{ node.title.substr(0, node.title.indexOf(searchValue)) }}
              <span style="color: #f50">{{ searchValue }}</span>
              {{ node.title.substr(node.title.indexOf(searchValue) + searchValue.length) }}
            </span>
            <span v-else>{{ node.title }}</span>

            <div class="icon-group">
              <a-icon type="plus" class="action-icon" @click.stop="nodeHandle(node, 'add')"></a-icon>
              <a-icon
                type="edit"
                v-show="node.parentId != null"
                class="action-icon"
                @click.stop="nodeHandle(node, 'edit')"
              ></a-icon>
              <a-icon
                type="delete"
                v-show="node.parentId != null"
                class="action-icon"
                @click.stop="nodeHandle(node, 'delete')"
              ></a-icon>
            </div>
          </div>
        </template>
      </AntTree>
    </div>

    <a-modal v-model="modalVisible" :title="modalTitle" ok-text="确定" @ok="handleOk" @cancel="handleCancel">
      <a-form-model
        ref="form"
        :model="form"
        :rules="{ name: [{ required: true, message: '请输入名称', trigger: 'blur' }] }"
      >
        <a-form-model-item label="名称" prop="name" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-input v-model="form.name" allowClear />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script lang="jsx">
  import AntTree from 'ant-design-vue/es/tree'
  import { allIcons } from '@/assets/icons/index'
  import { getTreeItem } from '@/utils/getMapFlatTree'

  const getParentKey = (key, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children)
        }
      }
    }
    return parentKey
  }

  function handleTreeData(data) {
    data.forEach(item => {
      item['disabled'] = item.isLeaf
      if (item.children) {
        handleTreeData(item.children)
      }
    })
    return data
  }

  const districts = ['0', '1', '2', '3', '4', '5']
  function getIcon(ele) {
    // 行政区划icon
    if (districts.includes(ele.icon)) {
      return 'district_' + ele.icon
    }

    if (ele.children && ele.children.length > 0) {
      return 'catalogue'
    } else {
      if (ele.type === 'category') {
        if (ele?.icon) {
          return allIcons.includes(ele.icon) ? ele.icon : 'catalogue'
        }
        return 'catalogue'
      }
      if (ele.type === 'data') {
        if (ele?.icon) {
          return allIcons.includes(ele.icon) ? ele.icon : 'leaf'
        }
      }

      return 'leaf'
    }
  }

  function resetDataSource(data, replaceFields) {
    function dealData(arr) {
      arr.forEach((ele, i) => {
        arr[i] = {
          ...ele,
          key: ele?.[replaceFields.key],
          title: ele?.[replaceFields.title],
          children: ele?.[replaceFields.children],
          slots: { icon: getIcon(ele) },
          scopedSlots: { title: 'title' },
        }

        dealData(ele?.[replaceFields.children] || [])
      })
    }
    dealData(data)

    return data
  }

  export default {
    name: 'ActionTree',
    components: { AntTree },
    props: {
      treeOptions: {
        type: Object,
        required: true,
      },
      isLeafDisabled: {
        type: Boolean,
      },
      defaultExpandedKeys: {
        type: Array,
        default: () => [],
      },
      hasTab: {
        type: Boolean,
        default: false,
      },
      currentKeys: { default: () => [] },
    },

    data() {
      return {
        loading: false,
        treeData: [],

        expandedKeys: this.defaultExpandedKeys,
        key: this.treeOptions.replaceFields.key,
        leafNodes: [],
        searchValue: '',
        autoExpandParent: true,

        dataList: [],
        selectedKeys: this.currentKeys,

        allIcons: allIcons,

        modalVisible: false,
        modalTitle: '',
        form: { name: undefined },
        modalType: '',
        actionNode: undefined,
      }
    },
    filters: {},
    created() {
      this.getDataSource(undefined, 'created')
    },
    watch: {
      currentKeys(newVal) {
        this.selectedKeys = newVal
      },
    },
    methods: {
      nodeHandle(node, type) {
        this.modalType = type
        this.actionNode = node
        switch (type) {
          case 'add':
            this.modalTitle = '新增'
            this.modalVisible = true
            break
          case 'edit':
            this.modalTitle = '编辑'
            this.modalVisible = true
            this.form.name = node.title
            break
          case 'delete':
            this.handleDelete(node)
            break
        }
      },
      handleCancel() {
        this.modalVisible = false
      },
      handleOk() {
        this.$refs['form'].validate(valid => {
          if (!valid) return
          if (this.modalType == 'add') {
            this.treeOptions.addApi(this.actionNode, this.form.name, data => {
              this.$message.success(`新增成功`, 3)
              this.modalVisible = false
              this.form.name = undefined
              this.getDataSource(undefined, 'add', data)
            })
          }
          if (this.modalType == 'edit') {
            this.treeOptions.editApi(this.actionNode, this.form.name, () => {
              this.$message.success(`更新成功`, 3)
              this.modalVisible = false
              this.form.name = undefined
              this.getDataSource(undefined, 'update')
            })
          }
        })
      },
      handleDelete(node) {
        const Ids = node[this.treeOptions.replaceFields.key]
        this.$confirm({
          title: '确认删除所选中数据?',
          onOk: () => {
            return this.treeOptions.deleteApi({ [this.treeOptions.replaceFields.key + 's']: Ids }).then(res => {
              this.$message.success(`成功删除`, 3)
              this.getDataSource(undefined, 'update')
              this.visible = false
            })
          },
          onCancel() {},
        })
      },
      getExpandedKeys(nodes) {
        if (!nodes || nodes.length === 0) {
          return []
        }

        nodes.forEach(node => {
          this.leafNodes.push(node.key)
          return this.getExpandedKeys(node.children)
        })
      },
      generateList(data) {
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          const key = node.key
          const title = node.title
          this.dataList.push({ key, title })
          if (node.children) {
            this.generateList(node.children)
          }
        }
      },
      // 筛选节点
      onChange(e) {
        const value = e.target.value
        const expandedKeys = this.dataList
          .map(item => {
            if (item.title.indexOf(value) > -1) {
              return getParentKey(item.key, this.treeData)
            }
            return null
          })
          .filter((item, i, self) => item && self.indexOf(item) === i)

        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        })
      },
      // 获取树
      getDataSource(value, type, activeId) {
        this.loading = true
        if (!this.treeOptions?.getDataApi) {
          this.loading = false

          this.treeData = resetDataSource(this.treeOptions.dataSource, this.treeOptions.replaceFields)

          // 设置默认选中第一个节点
          if (this.selectedKeys.length == 0 && type === 'created') {
            this.selectedKeys = [this.treeData[0].key]
          }

          this.generateList(this.treeData)
          this.getExpandedKeys(this.treeData)
          Object.assign(this, {
            expandedKeys: this.leafNodes,
            searchValue: value,
            autoExpandParent: true,
          })
          this.leafNodes = []
          if (type === 'created' || type === 'update' || type === 'add') {
            this.$emit('onTreeMounted', this.treeData)
          }
        } else {
          setTimeout(() => {
            const searchInfo = { keywords: value }
            this.treeOptions
              .getDataApi(searchInfo)
              .then(response => {
                this.loading = false
                if (this.isLeafDisabled) {
                  this.treeData = handleTreeData(response?.data || [])
                }
                this.treeData = resetDataSource(response?.data || [], this.treeOptions.replaceFields)

                // 设置默认选中第一个节点
                if (this.selectedKeys.length == 0 && type === 'created') {
                  this.selectedKeys = [this.treeData[0].key]
                }

                this.generateList(this.treeData)

                if (type === 'created') {
                  this.getExpandedKeys(response.data)
                  this.expandedKeys = this.leafNodes
                }

                Object.assign(this, {
                  searchValue: value,
                })
                // this.leafNodes = []
                if (type === 'created') {
                  this.$emit('onTreeMounted', response?.data || [])
                }
                if (type === 'add') {
                  const item = getTreeItem(this.treeData, { keyName: 'key', keyValue: activeId })
                  this.handleNodeClick([], item, 'manual')

                  this.$emit('onTreeUpdate', response?.data || [], item)
                }
                if (type === 'update') {
                  const item = getTreeItem(this.treeData, { keyName: 'key', keyValue: this.selectedKeys[0] })
                  this.$emit('onTreeUpdate', response?.data || [], item)
                }
              })
              .catch(res => {
                console.log('error', res)
              })
          }, 200)
        }
      },
      // 节点单击事件,
      handleNodeClick(keys, event, type) {
        if (type === 'manual') {
          this.selectedKeys = [event.key]
          this.$emit('select', event)
          return
        }
        if (!keys?.length) return
        this.selectedKeys = [event.node.eventKey]
        this.$emit('select', event.node.$options.propsData.dataRef)
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys
        this.autoExpandParent = false
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep li .ant-tree-node-content-wrapper {
    display: inline-block;
    width: calc(100% - 20px);
  }
  ::v-deep li .ant-tree-node-content-wrapper {
    display: inline-block;
    width: calc(100% - 20px);
  }

  ::v-deep li.ant-tree-treenode-disabled > .ant-tree-node-content-wrapper span {
    color: #aaa;
  }
  ::v-deep .ant-tree > li:last-child {
    padding-bottom: 0;
    // margin-bottom: 7px;
  }
  ::v-deep .ant-tree > li:first-child {
    padding-top: 0;
    // margin-top: 7px;
  }

  // 去掉叶子前的icon
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher.ant-tree-switcher-noop .anticon-file {
    display: none;
  }
  // 去掉叶子前line
  ::v-deep
    .ant-tree.ant-tree-show-line
    .ant-tree-child-tree
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }
  ::v-deep
    .ant-tree.ant-tree-show-line
    li:not(.ant-tree-treenode-switcher-open):not(.ant-tree-treenode-switcher-close):has(
      span.ant-tree-switcher.ant-tree-switcher-noop
    )::before {
    display: none;
  }

  // 展开箭头
  ::v-deep .ant-tree.ant-tree-show-line li span.ant-tree-switcher .ant-tree-switcher-icon {
    color: #666;
    font-size: 14px;
  }

  .loading {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
    top: 30px;
  }

  .action-icon {
    padding: 0 3px;
    margin-left: 0 !important;
  }

  .icon-group {
    position: absolute;
    right: -5px;
    top: 0;
    height: 100%;
    display: none;
    background: #fff;
    padding: 0 3px;
  }
  .container {
    display: inline-block;
    width: calc(100% - 20px);
    position: relative;
    // display: flex; /* 创建一个弹性容器 */
    // justify-content: center; /* 水平居中容器内的所有元素 */
    // align-items: center; /* 垂直居中容器内的所有元素 */
    &:hover {
      .icon-group {
        display: block;
      }
    }
  }
</style>
