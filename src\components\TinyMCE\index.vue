<template>
  <div class="tinymce-editor">
    <Editor
      v-model="myValue"
      id="tinyMceContainer"
      ref="tinyMceRef"
      :init="init"
      :disabled="disabled"
      @onClick="onClick"
    ></Editor>
    <!-- :api-key="abcdefghijklmnopqrst1234567890" -->
    <!-- <vue-tinymce
      ref="tinymce"
      @input="$emit('input', content)"
      v-model="content"
      :init="setting">
    </vue-tinymce> -->
  </div>
</template>

<script lang="jsx">
  import tinymce from 'tinymce/tinymce'
  import Editor from '@tinymce/tinymce-vue'
  import 'tinymce/themes/silver/theme'
  import 'tinymce/icons/default'
  // import 'tinymce/icons/default/icons'

  import 'tinymce/plugins/contextmenu'
  import 'tinymce/plugins/colorpicker'

  import 'tinymce/plugins/textcolor' //颜色
  import 'tinymce/plugins/advlist' //高级列表
  import 'tinymce/plugins/autolink' //自动链接
  import 'tinymce/plugins/link' //超链接
  import 'tinymce/plugins/image' //插入编辑图片
  import 'tinymce/plugins/lists' //列表插件
  import 'tinymce/plugins/charmap' //特殊字符
  import 'tinymce/plugins/media' //插入编辑媒体
  import 'tinymce/plugins/wordcount' // 字数统计
  import 'tinymce/plugins/table'
  import 'tinymce/plugins/fullscreen'
  import 'tinymce/plugins/preview'
  import 'tinymce/plugins/imagetools'
  // import '/static/tinymce/plugins/indent2em' //首行缩进
  import '../../../public/static/tinymce/plugins/indent2em' //首行缩进
  import 'tinymce/skins/ui/oxide/skin.min.css'
  import 'tinymce/plugins/code'

  // import 'tinymce/plugins/fontselect'

  import PluginManagerLetterspacing from './js/latterspacing.js'
  import { uploadMinioFile } from '@/api/common'

  import uuid4 from '@/utils/guid.js'
  import { Bufferfrom, Minio } from 'minio-vite-js'

  function getBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = error => reject(error)
    })
  }

  function getFileName(url) {
    let arr = url.split('?')[0].split('/')
    const str = arr[arr.length - 1]

    if (arr.length > 1) {
      return decodeURIComponent(str)
    } else {
      return ''
    }
  }

  export default {
    components: {
      Editor,
    },
    props: {
      //传入一个value，使组件支持v-model绑定
      value: {
        type: String,
        default: '',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      plugins: {
        type: [String, Array],
        default:
          // 'advlist autolink code paste textcolor colorpicker fullscreen link lists image media table imagetools ',
          'advlist autolink code paste textcolor colorpicker fullscreen link lists image media table imagetools removeformat indent2em',
      },
      toolbar: {
        type: [String, Array],
        default:
          // 'bold italic underline strikethrough | styleselect fontsizeselect fontselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | cut copy paste | bullist numlist | outdent indent blockquote | undo redo | link unlink code | removeformat'
          // ' undo redo | alignleft aligncenter alignright alignjustify | fontsizeselect |  forecolor | outdent indent|  bold italic underline blockquote | h2 p lists image media table link |  styleselect |fontselect | bullist numlist  |fullscreen code | removeformat| hr',
          ' undo redo | alignleft aligncenter alignright  | fontsizeselect |  forecolor |  indent2em|  bold italic underline   | h2 p lists image table link | removeformat|  styleselect |fontselect |fullscreen | hr',
        // ' undo redo | alignleft aligncenter alignright  | fontsizeselect |  forecolor |  indent2em|  bold italic underline | bullist numlist  | h2 p lists image table link | removeformat|  styleselect |fontselect |fullscreen | hr',
        // ' undo redo | alignleft aligncenter alignright alignjustify | fontsizeselect |  forecolor | outdent indent|  bold italic underline blockquote |indent2em| h2 p lists image media table link |  styleselect |fontselect | bullist numlist  |fullscreen code | removeformat| hr',
      },
    },
    data() {
      return {
        folderName: 'newsCover',
        minioClient: null,
        updateInterval: null,
        //初始化配置
        init: {
          selector: '#tinyMceContainer',
          language_url: '/static/tinymce/langs/zh_CN.js',
          language: 'zh_CN',
          skin_url: '/static/tinymce/skins/ui/oxide',
          height: 300,
          plugins: this.plugins,
          toolbar: this.toolbar,
          fontsize_formats: '8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 36pt',
          font_formats:
            '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;新罗马字体=TimeNewRoman;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;苹方粗体=PingFangBold;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
          // font_formats:
          //   "微软雅黑='PingFang SC,微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;DINAlternateBold=DINAlternateBold;PingFangSC=PingFang SC;PingFangBold=PingFangBold;Arial=arial,helvetica,sans-serif;",
          // font_formats: 'DINAlternateBold=DINAlternateBold;PingFangSC=PingFang SC;PingFangBold=PingFangBold;',
          // font_formats:
          //   "微软雅黑='微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';幼圆='幼圆';DINAlternateBold=DINAlternateBold;PingFangSC=PingFang SC;PingFangBold=PingFangBold;Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings",

          branding: false,
          menubar: false,
          // forced_root_block: '', // 删除在tinymce中自动添加的p标签
          indent_use_margin: true,
          // style_formats: [
          //   {
          //     title: '首行缩进',
          //     block: 'div',
          //     styles: { 'text-indent': '2em', 'padding-left': '0' },
          //   },
          // ],
          // //此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
          // //如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler
          // images_upload_handler: (blobInfo, success, failure) => {
          //   console.log('blobInfo, success, failure', blobInfo, success, failure)
          //   const img = 'data:image/jpeg;base64,' + blobInfo.base64()
          //   success(img)
          // },
          // resize: false
          file_picker_types: 'media',
          //初始化前执行
          setup: editor => {},
          //实例化执行
          init_instance_callback: editor => {
            this.content && editor.setContent(this.content)
            //this.hasInit = true
            editor.on('NodeChange Change KeyUp SetContent', () => {
              //this.hasChange = true
              this.content = editor.getContent()
            })
          },
          //上传图片回调
          images_upload_handler: (blobInfo, success, failure) => {
            this.handleImgUpload(blobInfo, success, failure)
          },
        },
        myValue: this.value,
      }
    },

    created() {
      const urlObj = new URL(process.env.VUE_APP_MINIO_URL)
      //连接minio文件服务器
      this.minioClient = new Minio.Client({
        endPoint: urlObj.hostname, //对象存储服务的URL
        port: +urlObj.port, //端口号
        useSSL: urlObj.protocol === 'https:', //true代表使用HTTPS
        accessKey: process.env.VUE_APP_MINIO_ACCESSKEY, //账户id
        secretKey: process.env.VUE_APP_MINIO_SECRETKEY, //密码
        partSize: '50M',
      })
    },
    mounted() {
      this.initialize() // 初始化
    },
    beforeDestroy() {
      console.log('tinymce beforeDestroy', window, tinymce)
      tinymce.remove()
      window.tinymce.remove()
      window.tinyMCE.remove()
    },
    methods: {
      initialize() {
        setTimeout(() => {
          PluginManagerLetterspacing()
          this.myValue = this.value
        }, 100)
      },
      //添加相关的事件，可用的事件参照文档=> https://github.com/tinymce/tinymce-vue => All available events
      onClick(e) {
        this.$emit('onClick', e, tinymce)
      },

      //base64转blob
      toBlob(base64Data) {
        let byteString = base64Data
        if (base64Data.split(',')[0].indexOf('base64') >= 0) {
          byteString = atob(base64Data.split(',')[1]) // base64 解码
        } else {
          byteString = unescape(base64Data.split(',')[1])
        }
        // 获取文件类型
        let mimeString = base64Data.split(';')[0].split(':')[1] // mime类型

        // ArrayBuffer 对象用来表示通用的、固定长度的原始二进制数据缓冲区
        // let arrayBuffer = new ArrayBuffer(byteString.length) // 创建缓冲数组
        // let uintArr = new Uint8Array(arrayBuffer) // 创建视图

        let uintArr = new Uint8Array(byteString.length) // 创建视图

        for (let i = 0; i < byteString.length; i++) {
          uintArr[i] = byteString.charCodeAt(i)
        }
        // 生成blob
        const blob = new Blob([uintArr], {
          type: mimeString,
        })
        // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
        return blob
      },

      //上传图片
      handleImgUpload(blobInfo, success, failure) {
        let formdata = new FormData()
        var file = blobInfo.blob()
        console.log('上传照片 01', blobInfo, success, failure)
        //获取文件类型及大小
        const fileName = file.name
        const mineType = file.type
        const fileSize = file.size
        //参数
        let metadata = {
          'content-type': mineType,
          'content-length': fileSize,
        }
        if (!file.name) {
          const suffix = file.type == 'image/png' ? 'png' : file.type == 'image/gif' ? 'gif' : 'jpg'
          file = new File([file], `test.${suffix}`)
        }
        // if (file.size > 5 * 1024 * 1024) {
        //   this.$message('图片不能超过5M')
        //   return false
        // }
        // formdata.set('file', file)

        let reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onloadend = e => {
          const dataurl = e.target.result
          //base64转blob
          const blob = this.toBlob(dataurl)
          //blob转arrayBuffer
          let reader2 = new FileReader()
          reader2.readAsArrayBuffer(blob)

          reader2.onload = ex => {
            //定义流
            let res = ex.target.result //ArrayBuffer
            let buf = Bufferfrom(res) //Buffer

            const bucketName = process.env.VUE_APP_MINIO_BUCKET
            const pathName = this.folderName ? `${this.folderName}/${fileName}` : fileName

            let arr = pathName.split('.')

            const pathNameAndUid = `${arr.slice(0, arr.length - 1).join('.')}${
              this.hasUuid ? '-uuid-' + uuid4() : ''
            }.${arr[arr.length - 1]}`

            this.minioClient.putObject(bucketName, pathNameAndUid, buf, fileSize, metadata, (error, etag) => {
              if (error == null) {
                const fileUrl = `${process.env.VUE_APP_MINIO_URL}/${bucketName}/${pathNameAndUid}`
                //输出url
                let only = { url: fileUrl, name: pathNameAndUid }
                console.log(only)
                success(only.url)
              }
            })
          }
        }
      },
      //可以添加一些自己的自定义事件，如清空内容
      clear() {
        this.myValue = ''
      },
    },
    watch: {
      value(newValue) {
        this.myValue = newValue
      },
      myValue(newValue) {
        this.$emit('input', newValue)
      },
    },
  }
</script>
<style scoped></style>
