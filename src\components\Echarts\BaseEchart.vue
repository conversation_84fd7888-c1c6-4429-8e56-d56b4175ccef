<template>
  <div :id="id" ref="echartsContainerRef" :class="className" :style="{ height: height, width: width }"></div>
</template>

<script lang="jsx">
  import * as echarts from 'echarts'
  import reszie from './resize'
  export default {
    mixins: [reszie],
    props: {
      className: {
        type: String,
        default: 'base-echart',
      },
      id: {
        type: String,
        default: 'base-echart',
      },
      width: {
        type: String,
        default: '600px',
      },
      height: {
        type: String,
        default: '300px',
      },
      option: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        chartIns: null,
      }
    },
    watch: {
      option() {
        this.drawChart()
      },
    },
    mounted() {
      this.init()
      this.drawChart()
    },
    beforeDestroy() {
      if (!this.chartIns) {
        return
      }
      this.chartIns.dispose()
      this.chartIns = null
    },
    methods: {
      init() {
        const dom = this.$refs.echartsContainerRef
        this.chartIns = echarts.init(dom)

        this.$nextTick(() => {
          setTimeout(() => {
            this.$listeners?.getEchartsIns?.(this.chartIns)
          }, 10)
        })
      },
      drawChart() {
        this.chartIns.setOption(this.option, true)

        this.$nextTick(() => {
          setTimeout(() => {
            this.chartIns.resize()
          }, 10)
        })
      },
    },
  }
</script>

<style scoped></style>
