<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <TreeGeneral
        style="width: 220px"
        ref="treeGeneralRef"
        :current-keys="treeSelectKey"
        :treeOptions="treeOptions"
        @onTreeMounted="onTreeMounted"
        @onTreeUpdate="onTreeUpdate"
        @select="clickTreeNode"
      />
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel" style="background: #ffffff">
      <div class="top-action-bar">
        <div>
          <a-button type="primary" @click="handleUpload()" style="margin-right: 10px">
            <a-icon type="upload" />
            上传文件
          </a-button>
          <a-button type="default" @click="handleAdd()" style="margin-right: 10px">
            <a-icon type="plus" />
            新建文件夹
          </a-button>
          <a-button type="danger" v-if="isChecked" @click="handleDelete">
            <a-icon type="delete" />
            删除
          </a-button>
        </div>
        <a-input
          v-model="keywords"
          placeholder="请输入文件名"
          allow-clear
          @keyup.enter.native="handleQuery"
          @change="handleQuery"
          style="width: 240px"
        />
      </div>

      <div class="table-bar">
        <a-breadcrumb>
          <span v-for="(item, index) in breadcrumbPath" :key="index" @click="handleBreadcrumbClick(item)">
            <a-breadcrumb-item>
              <a-tooltip v-if="item.fileName.length > 4">
                <template slot="title">{{ item.fileName }}</template>
                {{ item.fileName.substr(0, 4) + '...' }}
              </a-tooltip>
              {{ item.fileName.length <= 4 ? item.fileName : '' }}
            </a-breadcrumb-item>
          </span>
        </a-breadcrumb>

        <div style="display: flex; gap: 14px; user-select: none">
          <div
            :style="{
              width: '32px',
              height: '32px',
              cursor: 'pointer',
              background: `url(${require('@/assets/images/cloud-refresh.png')}) no-repeat center / 100% 100%`,
            }"
            @click="getList"
          ></div>

          <div
            :style="{
              width: '32px',
              height: '32px',
              cursor: 'pointer',
              background: `url(${
                viewModel === 'list'
                  ? require('@/assets/images/cloud-brief.png')
                  : require('@/assets/images/cloud-list.png')
              }) no-repeat center / 100% 100%`,
            }"
            @click="viewModelSwitch"
          ></div>
        </div>
      </div>

      <VxeTable
        v-show="viewModel === 'list'"
        ref="vxeTableRef"
        :tableTitle="tableTitle"
        :columns="columns"
        :tableData="list"
        :loading="loading"
        :isShowTableHeader="false"
        :rowConfig="{ isHover: true, keyField: 'id' }"
        @selectChange="selectChange"
        @sortChange="sortChange"
        :tablePage="false"
      ></VxeTable>

      <BriefModel
        v-show="viewModel === 'brief'"
        :tableData="list"
        :loading="loading"
        :ids="ids"
        @onItemClick="onItemClick"
        @selectChange="selectChange"
        @handleDelete="handleDelete"
        @downloadFile="downloadFile"
      />
    </div>

    <FormModal v-if="showFormModal" ref="formModalRef" @ok="onOperationComplete" @close="showFormModal = false" />
    <UploadModal
      v-if="showUploadModal"
      ref="uploadModalRef"
      @ok="onOperationComplete"
      @close="showUploadModal = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getList, getFolderTree, deleteFn, addFolder, updateFolder, search } from './services'
  import FormModal from './modules/FormModal'
  import UploadModal from './modules/UploadModal.vue'
  import BriefModel from './BriefModel.vue'
  import TreeGeneral from './FileTree.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import getEquipmentTree from '@/utils/getEquipmentTree'
  import debounce from 'lodash/debounce'

  export default {
    name: 'CloudFile',
    components: {
      TreeGeneral,
      FormModal,
      VxeTable,
      VxeTableForm,
      UploadModal,
      BriefModel,
    },
    data() {
      return {
        treeSelectKey: [],
        treeDataSource: [],
        treeOptions: {
          getDataApi: getFolderTree,
          deleteApi: deleteFn,
          addApi: (node, name, callback) => {
            addFolder({ parentId: node.id, folderName: name }).then(res => {
              callback(res.data)
            })
          },
          editApi: (node, name, callback) => {
            updateFolder({ id: node.id, folderName: name }).then(res => {
              callback()
            })
          },
          replaceFields: {
            children: 'children',
            title: 'fileName',
            key: 'id',
          },
        },

        breadcrumbPath: [],

        showFormModal: false,
        showUploadModal: false,
        viewModel: 'list',
        keywords: undefined,

        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,

        queryParam: {
          id: undefined,
        },
        columns: [
          { type: 'checkbox', width: 40 },
          {
            field: 'fileName',
            minWidth: 280,
            slots: {
              header: () => {
                return (
                  <span>
                    文件名<span style='color: #86909c;margin-left:8px'>{this.list.length}</span>
                  </span>
                )
              },
              default: ({ row, rowIndex }) => {
                let iconUrl
                if (row.isFile === 0) {
                  iconUrl = require('@/assets/images/upload/folder.png')
                } else {
                  if (row.fileIcon === 'image') {
                    iconUrl = row.fileUrl
                  } else {
                    iconUrl = require(`@/assets/images/upload/${row.fileIcon}.png`)
                  }
                }

                return (
                  <div
                    style='cursor: pointer;display:flex;align-items: center;'
                    onClick={() => {
                      this.onItemClick(row)
                    }}
                  >
                    <div
                      class='icon'
                      style={{
                        width: '40px',
                        height: '40px',
                        marginRight: '8px',
                        background: `url(${iconUrl}) no-repeat center / contain`,
                      }}
                    />
                    <TableCellOverflow content={row.fileName} rowNum={1} style='flex: 1;' />
                  </div>
                )
              },
            },
          },
          {
            title: '　　',
            field: '',
            minWidth: 70,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span class='operation'>
                    <a-icon
                      class='operation-icon'
                      type='download'
                      style={{
                        'margin-right': '16px',
                        visibility: row.isFile === 1 ? 'visible' : 'hidden',
                      }}
                      onClick={() => {
                        this.downloadFile(row)
                      }}
                    />
                    <a-icon class='operation-icon' type='delete' onClick={() => this.handleDelete(row)} />
                  </span>
                )
              },
            },
          },
          {
            title: '大小',
            field: 'sizeHuman',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => (row.isFile === 1 ? row.sizeHuman : '-'),
            },
          },
          {
            title: '文件类型',
            field: 'fileType',
            minWidth: 100,
          },
          {
            title: '修改时间',
            field: 'updatedTime',
            minWidth: 150,
          },
        ],
        districtTypes: {},
      }
    },
    created() {},
    computed: {},
    watch: {},
    methods: {
      /** 查询列表 */
      getList() {
        this.showFormModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getList(this.queryParam).then(response => {
          this.list = response.data
          this.loading = false
        })
      },

      handleQuery: debounce(function () {
        this.$nextTick(() => {
          if (!this.keywords) {
            this.dealBreadcrumb(this.treeDataSource[0])
            return
          }
          this.dealBreadcrumb(this.treeDataSource[0], false)

          this.loading = true
          this.selectChange({ records: [] })
          search({ keywords: this.keywords }).then(res => {
            this.list = res.data
            this.loading = false
          })
        })
      }, 500),

      onItemClick(row) {
        if (row.isFile === 0) {
          this.dealBreadcrumb(row)
        }
        if (row.isFile === 1) {
          window.open(row.fileUrl)
        }
      },

      handleBreadcrumbClick(item) {
        this.dealBreadcrumb(item)
      },

      dealBreadcrumb(item, isGetList = true) {
        this.treeSelectKey = [item.id]
        this.queryParam.id = item.id
        this.tableTitle = item.fileName

        if (item.id === 0) {
          this.breadcrumbPath = [{ parentId: null, parentPath: item.parentPath, fileName: item.fileName, id: item.id }]

          isGetList && this.getList()
          return
        }
        const pathArr = item.parentPath
          .split(',')
          .map(el => Number(el))
          .concat(item.id)
        const result = []
        const dealPath = arr => {
          arr.forEach(el => {
            if (pathArr.includes(el.id)) {
              result.push({
                parentId: el.parentId,
                parentPath: el.parentPath,
                fileName: el.fileName,
                id: el.id,
              })
              dealPath(el.children)
            }
          })
        }
        dealPath(this.treeDataSource)

        this.breadcrumbPath = result

        isGetList && this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        this.treeDataSource = data

        this.dealBreadcrumb(data[0])
      },
      onTreeUpdate(data, item) {
        this.treeDataSource = data

        this.dealBreadcrumb(item)
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        this.$refs.treeGeneralRef.getDataSource()
      },

      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        // this.names = valObj.records.map(item => item.indexName)
        this.isChecked = !!valObj.records.length
      },
      viewModelSwitch() {
        this.viewModel = this.viewModel === 'list' ? 'brief' : 'list'
        if (this.viewModel === 'list') {
          const fullData = this.$refs.vxeTableRef.$refs.vxeTableRef.getTableData().fullData
          this.$refs.vxeTableRef.$refs.vxeTableRef.clearCheckboxRow()
          this.$refs.vxeTableRef.$refs.vxeTableRef.setCheckboxRow(
            fullData.filter(el => this.ids.includes(el.id)),
            true,
          )
        }
      },

      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({
          property: item.field,
          direction: item.order,
        }))
        this.getList()
      },

      clickTreeNode(item) {
        this.queryParam.id = item.id
        this.tableTitle = item.title

        this.dealBreadcrumb(item)
      },

      downloadFile(row) {
        fetch(row.fileUrl).then(res => {
          res.blob().then(blob => {
            const blobUrl = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = blobUrl
            a.download = row.fileName
            a.click()
            window.URL.revokeObjectURL(blobUrl)
          })
        })
      },

      handleUpload() {
        this.showUploadModal = true
        this.$nextTick(() => this.$refs.uploadModalRef.handleAdd(this.queryParam.id))
      },
      /* 新增 */
      handleAdd() {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleAdd(this.queryParam.id))
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormModal = true
        this.$nextTick(() => this.$refs.formModalRef.handleUpdate(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id ? [row.id] : this.ids
        // const names = row.indexName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          onOk: () => {
            return deleteFn({ ids: id.join(',') }).then(res => {
              this.$message.success(`成功删除 ${res.data} 条数据`, 3)
              this.selectChange({ records: [] })
              this.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');

  .top-action-bar {
    margin: 15px;
    display: flex;
    justify-content: space-between;
  }
  .table-bar {
    margin: 5px 15px 15px 15px;
    display: flex;
    justify-content: space-between;
  }

  ::v-deep .ant-breadcrumb {
    color: #4e5969;
    font-size: 15px;
    .ant-breadcrumb-separator {
      color: #4e5969;
      margin: 0;
    }
    & > span:last-child {
      color: #1d2129;
      font-weight: 700;
    }
    .ant-breadcrumb-link {
      cursor: pointer;
      user-select: none;
      &:hover {
        color: @primary-color;
      }
    }
  }

  .operation {
    display: none;
    .operation-icon {
      cursor: pointer;
      &:hover {
        color: @primary-color;
      }
    }
  }
  ::v-deep .vxe-body--row.row--hover {
    .operation {
      display: inline;
    }
  }

  ::v-deep .vxe-table--render-default.size--medium .vxe-body--column .vxe-cell {
    max-height: unset !important;
  }
</style>
