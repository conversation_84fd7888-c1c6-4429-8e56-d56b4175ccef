<template>
    <div style="position: relative; display: flex; flex-direction: column;  height: 7.7rem; width: 100%;  ">
        <!-- 地图 -->
        <div
            style="flex: 6;  height: 100%;  display: flex; flex-direction: row;justify-content: center; align-items: center;">
            <div style="flex: 6;width: 100%;height: 100%;  position: relative;">
                <Mapbox @onMapMounted="onMapMounted" mapBoxId="shuiDongLi" />

                <div class="curve-panel" v-if="!!activeProcess">
                    <div class="left">
                        <div class="header">
                            <div class="name">{{ activeProcess.name }}</div>
                        </div>

                        <div>
                            <div class="indicator">
                                <div class="label">水位:</div>
                                <div class="value">{{ activeProcess.wlv }}m</div>
                            </div>
                            <div class="indicator">
                                <div class="label">流量:</div>
                                <div class="value">{{ activeProcess.q }}m</div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-bottom: 10px">
                            <a-button type="primary" size="small" @click.stop="activeProcess = null">收起曲线</a-button>
                        </div>
                    </div>
                    <div class="right">
                        <LineEchart :height="'210px'" :width="'100%'" :dataSource="lineChartDataShuiZha"
                            :custom="lineChartCustomShuiZha">
                        </LineEchart>
                    </div>
                </div>

            </div>
            <!-- 表数据 -->
            <div style="flex: 4; width: 100%; height: 100%; margin-left: 10px; position: relative; ">
                <div
                    style="font-size: 14px;  position: absolute; top: 0; left: 0; z-index: 1000; height: 0.21rem; width: 100%;">
                    <div style="float: left;">{{ shuizhaName || '暂无' }}</div>
                    <div
                        style="float: right; height: 0.1rem; width: 0.1rem; margin-top: 0.07rem; border-radius: 0.058rem; background-color: red;margin-left: 0.05rem;margin-right: 0.15rem;">
                    </div>
                    <div style="float: right;font-size: 10px; margin-top: 0.02rem; margin-left: 0.05rem;">超警戒水位</div>
                    <div
                        style="float: right; height: 0.1rem; width: 0.1rem; margin-top: 0.07rem; margin-right: 0.08rem;border-radius: 0.058rem;background-color:  #000000e0;">
                    </div>
                    <div
                        style="float: right;font-size: 10px;margin-top: 0.02rem; margin-right: 0.05rem; margin-left: 0.01rem;">
                        正常水位</div>
                    <div style="float: right;font-size: 10px;margin-top: 0.02rem; margin-right: 0.1rem;">水闸水位标识：</div>
                </div>
                <VxeTable ref="vxeDrainageTableRef" size="small" :isShowTableHeader="false" style="margin-top: 4px;"
                    :header-cell-class-name="headerCellClassName" :row-class-name="rowClassName" :cell-class-name="cellClassName"
                    :columns="columnsDrainage" :tableData="listDrainage" :tablePage="false">
                </VxeTable>
            </div>

        </div>
        <div style="flex: 4; height: 100%; width: 100%; padding-left: 20px; display: flex; flex-direction: column;">
            <!-- 折线图 -->
            <div style="flex: 22; position: relative;">
                <div
                    style="position: absolute; top: 22px; left: calc(50% - 120px); width: 230px; height: 30px;z-index: 1000;font-size: 13px;">
                    {{ "当前时刻： " + currentTime }}
                </div>
                <div style="position: absolute; top: 10px; left: 90px; width: 200px; height: 30px;z-index: 1000;">
                    <a-select v-model="hedaoName" allowClear
                        style="width: 100%; height: 25px; font-weight: 400; font-size: 12px;" placeholder="请选择"
                        :options="hedaoOptions" show-search></a-select>
                </div>
                <LineEchart :height="'210px'" style="margin-top: 20px; " :dataSource="lineChartData"
                    :custom="lineChartCustom">
                </LineEchart>
            </div>

            <div style="width: 100%; flex: 1;">
                <TimePlaySlider v-if="times.length" :times="times" @onTimeChange="onTimeChange" style="flex: 1;" />
            </div>
        </div>
    </div>
</template>

<script lang="jsx">
import { getValueByKey, getOptions } from '@/api/common'
import Mapbox from '../Mapbox/index.vue'
import VxeTable from '@/components/VxeTable/index.vue'
import TimePlaySlider from './TimePlaySlider/index.vue'
import { mapBoundGeo } from '@/utils/mapBounds.js'
import axios from 'axios'
import LineEchart from '../Linechart/index.vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import { waterColors, extractData, getChartsData } from './Utils.js'
import arrow2 from '@/assets/images/arrow2.png'
import { mapboxPopup } from '../Mapbox/popup.js'
import { color } from 'echarts'
let lineInverse = [
    10029, 10036, 10038,
    20004, 20008, 20012,
    20017, 20019, 20020,
    20025, 20026, 10048
]
export default {
    name: 'ShuiDongLi',
    mapIns: null, // 地图实例
    hedaoOptions: [],
    showPopupItem: [],
    components: { VxeTable, Mapbox, LineEchart, TimePlaySlider },
    data() {
        return {
            activeProcess: null,
            waterColors,
            hedaoData: [],
            loading: false,
            hedaoName: '',
            lineChartData: [],
            lineChartCustom: {
                shortValue: true, // 缩写坐标值
                xLabel: '', // x轴名称
                yLabel: '水位(m)', //y轴名称
                yUnit: '', //y轴单位
                legend: true, // 图例
                showAreaStyle: true, // 颜色区域
                rYUnit: '', // 右侧y轴单位
                rYLabel: '流量(m³/s)', // 右侧y轴名称
                rYInverse: false, // 右侧y轴是否反向
                yNameLocation: "end",
                dataZoom: false,
                color: null,
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '5%',
                    top: '15%',
                    containLabel: true,
                },
                legendOptions: {
                    orient: 'horizontal',
                },
                legendTop: '1%',
                legendLeft: '70%',
                xAxisData: []
            },
            listDrainage: [],
            columnsDrainage: [
                { type: 'seq', title: '序号', width: 50 },
                {
                    title: '时间',
                    field: 'time',
                    minWidth: 180,
                    fixed: 'left',
                    showOverflow: 'tooltip',
                },
                {
                    title: '水位(m)',
                    field: 'wlevel',
                    minWidth: 180,
                    showOverflow: 'tooltip',
                },
                {
                    title: '流量(m³/s)',
                    field: 'q',
                    minWidth: 180,
                    showOverflow: 'tooltip',
                }
            ],
            currentTime: '', // 当前时间
            times: [],
            maxFlow: 0, // 最大流量
            POINTS: null,
            maxWlevel: null,
            maxQ: null,
            minWlevel: null,
            minQ: null,
            max: 0, // 最大
            min: 0, // 最小
            selectedShow: "wlevel", // 0:水位，1:流量
            lineChartCustomShuiZha: {
                shortValue: true, // 缩写坐标值
                xLabel: '', // x轴名称
                yLabel: '水位(m)', //y轴名称
                yUnit: '', //y轴单位
                legend: true, // 图例
                showAreaStyle: true, // 颜色区域
                rYUnit: '', // 右侧y轴单位
                rYLabel: '流量(m³/s)', // 右侧y轴名称
                rYInverse: false, // 右侧y轴是否反向
                yNameLocation: "end",
                dataZoom: false,
                color: null,
                grid: {
                    left: '1%',
                    right: '1%',
                    bottom: '5%',
                    top: '15%',
                    containLabel: true,
                },
                legendOptions: {
                    orient: 'horizontal',
                },
                legendTop: '1%',
                legendLeft: '20%',
                xAxisData: []
            },
            lineChartDataShuiZha: [],
            shuizhaName: {}
        }
    },
    props: {
        modelCode: { // 模型名称
            type: String,
            default: "",
            required: true,
        },
    },
    watch: {
        async modelCode(newVal, oldVal) {
            /* ------------------------ 更新河道相关的数据 ----------------------- */
            if (!this.hedaoShpData) return;
            const tempShp = JSON.parse(JSON.stringify(this.hedaoShpData))
            for (let i = 0; i < tempShp.features.length; i++) {
                if (lineInverse.includes(tempShp.features[i].properties.id)) {
                    tempShp.features[i].geometry.coordinates.forEach(item => {
                        item.reverse()
                    })
                }
            }
            await this.refreshModelRes()
            tempShp.features = tempShp.features.filter(item => ('GQ' + item.properties.id) in this.hedaoData)
            this.mapIns.getSource('gq-line-source').setData(tempShp)
            let tempArr = []
            tempShp.features.forEach(item => {
                tempArr.push({
                    label: item.properties.object_name,
                    value: item.properties.id
                })
            })
            this.hedaoOptions = tempArr
            this.hedaoName = tempArr[0].value
            mapBoundGeo(tempShp, this.mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
        },
        hedaoName(newVal, oldVal) {
            if (newVal !== oldVal && this.currentTime) {
                this.refreshBottomChart(newVal)
            }
        },
        currentTime(newVal, oldVal) { // 时间轴改变时，重新获取数据
            if (newVal !== oldVal) {
                if (!this.mapIns) return
                this.handleOpenPopup()
                this.refreshBottomChart(this.hedaoName)
            }
        }
    },
    methods: {
        async refreshModelRes() {
            this.$emit('changeLoading', true)
            await axios.get(`${process.env.VUE_APP_MODEL_BASE}/${this.modelCode}.json`, {
                timeout: 60 * 1000
            })
                .then(res => {
                    this.hedaoData = res.data
                    let { times, POINTS, maxWlevel, maxQ, minWlevel, minQ } = extractData(res.data)
                    this.times = times;
                    this.POINTS = POINTS;
                    this.maxWlevel = maxWlevel;
                    this.maxQ = maxQ;
                    this.minWlevel = minWlevel;
                    this.minQ = minQ;

                    this.listDrainage = this.times.map(time => {
                        return {
                            time: time,
                            wlevel: parseFloat(this.POINTS.features[0].properties["wlevel-" + time]).toFixed(3),
                            q: parseFloat(this.POINTS.features[0].properties["q-" + time]).toFixed(3)
                        }
                    })
                    /* ------------------------ 更新水闸相关的数据 ----------------------- */
                    setTimeout(() => {
                        if (this.showPopupItem && this.showPopupItem.length) {
                            this.showPopupItem.forEach(el => {
                                el?.popupIns?.remove()
                            })
                        }
                        this.showPopupItem = POINTS.features.map(item => ({ ...item.properties, lngLat: item.geometry.coordinates }))
                        if (this.showPopupItem.length) {
                            this.shuizha = this.showPopupItem[0]
                            this.shuizhaName = this.shuizha.name
                            this.listDrainage = times.map(time => {
                                return {
                                    time: time,
                                    wlevel: parseFloat(this.shuizha["wlevel-" + time]).toFixed(3),
                                    q: parseFloat(this.shuizha["q-" + time]).toFixed(3)
                                }
                            })
                        }

                        this.handleOpenPopup()
                        this.mapIns.getSource('points').setData(JSON.parse(JSON.stringify(this.POINTS)))
                    }, 1000)

                    this.currentTime = this.times[0];
                }).finally(() => {
                    this.$emit('changeLoading', false)
                })
        },
        refreshBottomChart(newVal) {
            let { wlevel, q, stakes, maxWlevel, maxQ, minWlevel, minQ } = getChartsData("GQ" + newVal, this.currentTime, this.hedaoData)
            let data3 = []
            let data1 = []
            let data2 = []
            stakes.forEach((element, index) => {
                data3.push([stakes[index]])
                data1.push(+(wlevel[index]))
                data2.push(+(q[index]))
            });
            let res = [{
                name: '水位(m)',
                color: '#507EF7',
                yAxisIndex: 0,
                data: data1
            },
            {
                name: '流量(m³/s)',
                color: '#B5E241',
                yAxisIndex: 1,
                data: data2
            }]
            this.lineChartCustom.xAxisData = data3
            this.lineChartCustom.yMax0 = maxWlevel
            this.lineChartCustom.yMin0 = minWlevel
            this.lineChartCustom.yMax1 = maxQ
            this.lineChartCustom.yMin1 = minQ
            this.lineChartData = res
        },
        //添加以上两个图层之后 再添加这个函数，参数是动态线的图层配置
        addDashLayer(sourceLayerConfig) {
            const self = this
            const dashArraySequence = [
                [0, 4, 3],
                [0.5, 4, 2.5],
                [1, 4, 2],
                [1.5, 4, 1.5],
                [2, 4, 1],
                [2.5, 4, 0.5],
                [3, 4, 0],
                [0, 0.5, 3, 3.5],
                [0, 1, 3, 3],
                [0, 1.5, 3, 2.5],
                [0, 2, 3, 2],
                [0, 2.5, 3, 1.5],
                [0, 3, 3, 1],
                [0, 3.5, 3, 0.5]
            ];
            let step = 0;
            function animateDashArray(timestamp) {
                // Update line-dasharray using the next value in dashArraySequence. The
                // divisor in the expression `timestamp / 50` controls the animation speed.
                const newStep = parseInt(
                    (timestamp / 100) % dashArraySequence.length
                );

                if (newStep !== step) {
                    let layer = self.mapIns.getLayer(sourceLayerConfig.id); //获取图层
                    // debugger
                    if (layer) {
                        self.mapIns.setPaintProperty(
                            sourceLayerConfig.id,
                            'line-dasharray',
                            dashArraySequence[step]
                        );
                        step = newStep;
                    }
                }

                // Request the next frame of the animation.
                requestAnimationFrame(animateDashArray);
            }
            // start the animation
            animateDashArray(0);
        },
        async initMap(mapIns) {
            let hoveredPolylineId = null;
            const img1 = new Image();
            img1.src = arrow2;
            img1.onload = () => {
                mapIns.addImage('arrow2', img1, { sdf: true });
            }
            // 水系
            getOptions('main_canal').then(res => {
                axios(
                    // `${import.meta.env.VITE_GEOSERVER_BASE}/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=sthgq_vector:HP005`,
                    `https://www.sthzxgq.cn:11120/geoserver/sthgq_vector/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=sthgq_vector:HP005`,
                ).then(resp => {

                    this.hedaoShpData = resp.data;
                    const arr = []
                    res.data.forEach(el => {
                        arr.push(...[+el.key, el.option1])
                    })
                    /**
                    * 搜集河道线要素 object_name 为名称 id 为编码
                    */
                    let tempShp = JSON.parse(JSON.stringify(this.hedaoShpData))
                    for (let i = 0; i < tempShp.features.length; i++) {
                        if (lineInverse.includes(tempShp.features[i].properties.id)) {
                            tempShp.features[i].geometry.coordinates.forEach(item => {
                                item.reverse()
                            })
                        }
                    }
                    tempShp.features = tempShp.features.filter(item => {
                        return ('GQ' + item.properties.id) in this.hedaoData
                    })
                    let tempArr = []
                    tempShp.features.forEach(item => {
                        tempArr.push({
                            label: item.properties.object_name,
                            value: item.properties.id
                        })
                    })
                    this.hedaoOptions = tempArr
                    this.hedaoName = tempArr[0].value

                    mapBoundGeo(tempShp, mapIns, { top: 50, bottom: 50, left: 50, right: 50 })
                    mapIns.addSource('gq-line-source', {
                        type: 'geojson',
                        data: tempShp, //区划的面数据
                        'generateId': true // 确保所有特征都有唯一的ID
                    })

                    mapIns.addLayer({
                        'id': 'gq-line1',
                        'type': 'line',
                        'slot': 'middle',
                        'source': 'gq-line-source',
                        'layout': {
                            'line-join': 'round',
                            'line-cap': 'round'
                        },
                        'paint': {
                            // "line-pattern": "arrowsdf",
                            'line-color': "rgba(255,255,255,0.7)",
                            'line-width': ['case',
                                ['boolean', ['feature-state', 'hover'], false],
                                5,
                                [
                                    'case',
                                    ['==', ['get', 'id'], 10004],
                                    5,
                                    ['==', ['get', 'id'], 10005],
                                    5,
                                    ['==', ['get', 'id'], 20005],
                                    6,
                                    3,
                                ]]
                        }
                    });

                    mapIns.addLayer({
                        'id': 'gq-line2',
                        'type': 'line',
                        'slot': 'middle',
                        'source': 'gq-line-source',
                        'layout': {
                            'line-join': 'round',
                            'line-cap': 'round'
                        },
                        'paint': {
                            "line-pattern": "arrow2",
                            "line-pattern-cross-fade": 0.5,
                            'line-color': "#09Acff",
                            "line-opacity": 0.8,
                            'line-width': ['case',
                                ['boolean', ['feature-state', 'hover'], false],
                                5,
                                [
                                    'case',
                                    ['==', ['get', 'id'], 10004],
                                    12,
                                    ['==', ['get', 'id'], 10005],
                                    12,
                                    ['==', ['get', 'id'], 20005],
                                    15,
                                    10,
                                ]],
                            "line-dasharray": [2, 30],
                            // 'line-parttern':'arrowsdf'
                        }
                    });

                    mapIns.addLayer({
                        'id': 'gq-line',
                        'type': 'line',
                        'slot': 'middle',
                        'source': 'gq-line-source',
                        'layout': {
                            "visibility": "visible"
                        },
                        'paint': {
                            'line-color': "#09Acff",
                            'line-width': ['case',
                                ['boolean', ['feature-state', 'hover'], false],
                                5,
                                [
                                    'case',
                                    ['==', ['get', 'id'], 10004],
                                    3,
                                    ['==', ['get', 'id'], 10005],
                                    3,
                                    ['==', ['get', 'id'], 20005],
                                    3,
                                    2,
                                ]]
                        }
                    });
                    this.addDashLayer({
                        'id': 'gq-line'
                    })
                    mapIns.addInteraction('gq-line-click-interaction', {
                        type: 'click',
                        target: { layerId: 'gq-line' },
                        handler: (e) => {
                            // Copy coordinates array.
                            const coordinates = e.lngLat;
                            const outerHtml = document.createElement('div');
                            outerHtml.className = 'outerPaiShuiLine';
                            const description = e.feature.properties.object_name;
                            this.hedaoName = this.hedaoOptions.find(item => item.label == description).value;
                            outerHtml.textContent = description;
                            // this.extractData(e.feature.properties.id)
                            new mapboxgl.Popup()
                                .setLngLat(coordinates)
                                .setDOMContent(outerHtml)
                                .addTo(mapIns);
                        }
                    });
                    mapIns.on('mousemove', 'gq-line', (e) => {
                        if (e.features.length > 0) {
                            if (hoveredPolylineId !== null) {
                                mapIns.setFeatureState(
                                    { source: 'gq-line-source', id: hoveredPolylineId },
                                    { hover: false }
                                );
                            }
                            hoveredPolylineId = e.features[0].id;
                            mapIns.setFeatureState(
                                { source: 'gq-line-source', id: hoveredPolylineId },
                                { hover: true }
                            );
                        }
                    });
                    mapIns.on('mouseleave', 'gq-line', () => {
                        if (hoveredPolylineId !== null) {
                            mapIns.setFeatureState(
                                { source: 'gq-line-source', id: hoveredPolylineId },
                                { hover: false }
                            );
                        }
                        hoveredPolylineId = null;
                    });
                })
            })

            mapIns.addSource('points', {
                type: 'geojson',
                data: JSON.parse(JSON.stringify(this.POINTS)), //区划的面数据
                'generateId': true // 确保所有特征都有唯一的ID
            })
            mapIns.addLayer({
                'id': 'gq-area-points',
                'type': 'circle',
                'slot': 'top',
                'source': 'points',
                'paint': {
                    'circle-color': '#09Acff',
                    'circle-radius': 6,
                    'circle-stroke-width': 2,
                    'circle-stroke-color': '#fff'
                }
            });

            mapIns.addInteraction('gq-area-points-click-interaction', {
                type: 'click',
                target: { layerId: 'gq-area-points' },
                handler: (e) => {
                    // Copy coordinates array.
                    const coordinates = e.lngLat;
                    let index = this.showPopupItem.findIndex(el => el.name == e.feature.properties.name)
                    if (index != -1) return
                    let curr = e.feature.properties
                    const popupIns = mapboxPopup(this.mapIns, {
                        ...curr,
                        time: this.currentTime,
                        lngLat: coordinates,
                        onPopupClose: item => {
                            const index = this.showPopupItem.findIndex(el => el.name == item.name)
                            if (index === -1) return
                            this.showPopupItem[index].popupIns.remove()
                            this.showPopupItem = this.showPopupItem.filter((el, i) => i !== index)
                        },
                        onProcessClick: item => {
                            console.log("item = ", item)
                            let times = []
                            let wlv = []
                            let q = []
                            for (let key in item) {
                                if (key.startsWith('q-')) {
                                    times.push(key.substring(2))
                                    q.push(item[key])
                                }
                                if (key.startsWith('wlevel-')) {
                                    wlv.push(item[key])
                                }
                            }

                            let chartData = [{
                                name: '水位',
                                color: '#507EF7',
                                yAxisIndex: 0,
                                data: wlv
                            },
                            {
                                name: '流量',
                                color: '#B5E241',
                                yAxisIndex: 1,
                                data: q
                            }]
                            this.lineChartCustomShuiZha.xAxisData = times
                            this.lineChartCustomShuiZha.yMax0 = Math.max(...wlv)
                            this.lineChartCustomShuiZha.yMin0 = Math.min(...wlv)
                            this.lineChartCustomShuiZha.yMax1 = Math.max(...q)
                            this.lineChartCustomShuiZha.yMin1 = Math.min(...q)
                            this.lineChartDataShuiZha = chartData

                            this.activeProcess = { wlv: item["wlevel-" + this.currentTime] || 0, q: item["q-" + this.currentTime] || 0, name: item.name }
                        },
                    })
                    popupIns.getElement().style['z-index'] = '11'
                    this.showPopupItem.push({ name: curr.name, popupIns })
                }
            });

            mapIns.resize();
        },
        onTimeChange(time) {
            this.currentTime = time
        },
        handleOpenPopup() {
            if (this.showPopupItem && this.showPopupItem.length > 0 && !!this.mapIns) {
                this.showPopupItem.forEach(el => {
                    el?.popupIns?.remove()
                    this.dealPopup(el)
                })
            }
        },
        dealPopup(curr) {
            const popupIns = mapboxPopup(this.mapIns, {
                ...curr,
                time: this.currentTime,
                onPopupClose: item => {
                    const index = this.showPopupItem.findIndex(el => el.name == item.name)
                    if (index === -1) return
                    this.showPopupItem[index].popupIns.remove()
                    this.showPopupItem = this.showPopupItem.filter((el, i) => i !== index)
                },
                onProcessClick: item => {
                    let times = []
                    let wlv = []
                    let q = []
                    for (let key in item) {
                        if (key.startsWith('q-')) {
                            times.push(key.substring(2))
                            q.push(item[key])
                        }
                        if (key.startsWith('wlevel-')) {
                            wlv.push(item[key])
                        }
                    }
                    this.shuizhaName = item.name
                    this.listDrainage = times.map(time => {
                        return {
                            time: time,
                            wlevel: parseFloat(item["wlevel-" + time]).toFixed(3),
                            q: parseFloat(item["q-" + time]).toFixed(3)
                        }
                    })

                    let chartData = [{
                        name: '水位',
                        color: '#507EF7',
                        yAxisIndex: 0,
                        data: wlv
                    },
                    {
                        name: '流量',
                        color: '#B5E241',
                        yAxisIndex: 1,
                        data: q
                    }]
                    this.lineChartCustomShuiZha.xAxisData = times
                    this.lineChartCustomShuiZha.yMax0 = Math.max(...wlv)
                    this.lineChartCustomShuiZha.yMin0 = Math.min(...wlv)
                    this.lineChartCustomShuiZha.yMax1 = Math.max(...q)
                    this.lineChartCustomShuiZha.yMin1 = Math.min(...q)
                    this.lineChartDataShuiZha = chartData

                    this.activeProcess = { wlv: item["wlevel-" + this.currentTime] || 0, q: item["q-" + this.currentTime] || 0, name: item.name }
                },
            })

            popupIns.getElement().style['z-index'] = '11'
            let index = this.showPopupItem.findIndex(el => el.name == curr.name)
            if (index === -1) {
                this.showPopupItem.push({ name: curr.name, popupIns })
            } else {
                this.showPopupItem[index] = { ...this.showPopupItem[index], popupIns }
            }
        },
        getColorByValue(value) { // 根据值获取颜色
            const maxValue = this.maxFlow // 假设最大值为100
            const index = Math.floor((value / maxValue) * (waterColors.length - 1)) // 计算索引
            return waterColors[index] // 返回对应的颜色
        },
        getSizeByValue(value) { // 根据值获取颜色
            const maxValue = this.maxFlow
            const size = Math.floor(value / maxValue) * 10 // 计算大小
            return size ? size : 0
        },
        // 表头样式
        headerCellClassName({ column }) {
            return 'col-blue'
        },
        // 行样式
        rowClassName() {
            return 'row-green'
        },
        cellClassName({ row, column }) { // 单元格样式
            if (row?.wlevel >= 5.4) { // 假设要修改的列名为'name'
                return 'row-red' 
            }
        },
        async onMapMounted(mapIns) {
            await this.refreshModelRes() // 刷新模型数据

            this.mapIns = mapIns
            await this.initMap(mapIns)
            this.handleOpenPopup()
        }
    },
}

</script>
<style lang="less" scoped>
::v-deep .mapboxgl-popup {
    max-width: 500px !important;

    .mapboxgl-popup-content {
        padding: 0px;

        .mapboxgl-popup-close-button {
            color: #fff;
        }
    }
}

::v-deep .row-red {
  color: red;
}

::v-deep .outerPaiShuiLine {
    width: 150px;
    // height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: #fff;
    background-color: rgb(83, 132, 254);
}

::v-deep .outerPaiShui {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .innerTop {
        width: 100%;
        // height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        background-color: rgb(83, 132, 254);
    }

    .innerBottom {
        // height: 40px;
        // line-height: 40px;
        // background-color: red;
        text-align: center;
        font-size: 11px;
        font-weight: 350;
        color: #000000;
        margin-top: 5px;
        // margin-bottom: 5px;
    }

    .showChart {
        // background-color: red;
        color: rgb(83, 132, 254);
        // margin-top: 5px;
        font-size: 11px;
        font-weight: 350;
        margin-right: -50%;
        margin-bottom: 5px;
        cursor: pointer;
    }
}

::v-deep .outerPaiShui1 {
    .innerTop {
        width: 100%;
        // height: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        color: #fff;
        background-color: rgb(83, 132, 254);
    }

    .chart {
        height: 300px;
        width: 500px;
        background-color: #fff;
    }
}

.curve-panel {
    position: absolute;
    z-index: 1000;
    bottom: 4px;
    right: 4px;
    width: 700px;
    display: flex;
    border-radius: 4px;
    overflow: hidden;
    background: #ffffff;

    .left {
        border-right: 1px solid #e5e6eb;
        width: 120px;
        position: relative;
        display: flex;
        flex-direction: column;

        .header {
            background: #f2f3f5;
            font-weight: 500;
            color: #1d2129;
            line-height: 20px;
            padding: 6px 8px;
            display: flex;
            align-items: center;

            .icon {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: #0d9c47;
                color: #fff;
                display: inline-block;
                text-align: center;
                line-height: 20px;
            }

            .name {
                flex: 1;
                margin: 0 0 0 4px;
                overflow: hidden;
                font-size: 15px;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .indicator {
            display: flex;
            padding: 5px 15px;
            justify-content: space-between;

            .label {
                font-weight: 500;
                font-size: 12px;
                color: '#4E5969';
            }

            .value {
                font-weight: 500;
                font-size: 12px;
                color: #1d2129;
            }
        }
    }

    .right {
        flex: 1;
        padding-top: 10px;
    }
}
</style>
