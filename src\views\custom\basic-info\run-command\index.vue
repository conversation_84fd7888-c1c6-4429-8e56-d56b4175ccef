<template>
  <div class="common-table-page">
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="工作票编号">
        <a-input v-model="queryParam.cmdCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="工作票名称">
        <a-input v-model="queryParam.cmdName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="调度方案">
        <a-select
          show-search
          allow-clear
          v-model="queryParam.dispatchId"
          :options="dispatchOptions"
          placeholder="请选择"
          option-filter-prop="children"
        />
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          :tableTitle="tableTitle"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          :rowConfig="{ isCurrent: true, isHover: true }"
          :tablePage="{
            pageNum: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total,
          }"
          :isInModal="isDetail"
          :modalHeight="modalHeight"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button" v-if="!isDetail">
            <a-button type="primary" @click="handleAdd()" v-if="hasAddPermission">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete()">
              <a-icon type="delete" />
              删除
            </a-button>
            <!-- <a-button type="primary" @click="handleExport()">
              <a-icon type="download" />
              导出
            </a-button> -->
          </div>
        </VxeTable>
        <FormDrawer
          v-if="showForm"
          ref="formRef"
          @ok="onOperationComplete"
          @close="showForm = false"
          :dispatchOptions="dispatchOptions"
        />
        <RunCmdDetail 
          v-if="showDetail" 
          ref="detailRef" 
          @close="showForm = false" 
          @auditResult="handleAuditResult"
          @receiveResult="handleReceiveResult"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getPage, deleteRunmd } from './services'
  import { getDispatchPage } from '../dispatch-case/services'
  import FormDrawer from './modules/FormDrawer.vue'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import ExpandTable from './modules/ExpandTable.vue'
  import RunCmdDetail from './modules/RunCmdDetail.vue'

  import moment from 'moment'

  export default {
    name: 'RunCommand',
    components: {
      VxeTable,
      VxeTableForm,
      ExpandTable,
      FormDrawer,
      RunCmdDetail,
    },
    props: {
      isDetail: {
        type: Boolean,
        default: false,
      },
      type: {
        type: Number,
      },
      projectId: {
        type: Number,
        default: undefined,
      },
      modalHeight: {},
    },
    data() {
      return {
        isChecked: false,
        showForm: false,
        showDetail: false,
        dispatchOptions: [],
        showFormDetails: false,
        archivesOptions: [],
        list: [],
        tableTitle: '运行指令',
        loading: false,
        total: 0,
        selectIds: [],
        queryParam: {
          dispatchId: null,
          cmdName: '',
          cmdCode: '',
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          {
            type: 'seq',
            title: '序号',
            width: 50,
            slots: {
              default: ({ row, rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            type: 'expand',
            width: 30,
            fixed: 'left',
            slots: {
              content: ({ row, rowIndex }) => {
                return <ExpandTable row={row} />
              },
            },
          },
          {
            title: '工作票编号',
            field: 'cmdCode',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          // {
          //   title: '调度类型',
          //   field: 'dispatchType',
          //   minWidth: 100,
          //   showOverflow: 'tooltip',
          //   slots: {
          //     default: ({ row }) => {
          //       const typeMap = {
          //         1: '防汛调度',
          //         2: '灌溉调度',
          //         3: '生态调水'
          //       }
          //       return typeMap[row.dispatchType] || '-'
          //     }
          //   }
          // },
          // {
          //   title: '工程',
          //   field: 'projectName',
          //   minWidth: 120,
          //   showOverflow: 'tooltip',
          // },
          {
            title: '工作票名称',
            field: 'cmdName',
            minWidth: 140,
            showOverflow: 'tooltip',
          },
          {
            title: '调度方案',
            field: 'dispatchCode',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          // {
          //   title: '单位',
          //   field: 'deptName',
          //   minWidth: 120,
          //   showOverflow: 'tooltip',
          // },
          {
            title: '计划工作时间',
            field: 'planWorkTime',
            minWidth: 200,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                return `${row.planStartDate} - ${row.planEndDate}`
              }
            }
          },
          {
            title: '工作负责人',
            field: 'wardUserName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          // {
          //   title: '计划工作开始时间',
          //   field: 'planStartDate',
          //   minWidth: 180,
          //   showOverflow: 'tooltip',
          // },
          // {
          //   title: '计划工作结束时间',
          //   field: 'planEndDate',
          //   minWidth: 180,
          //   showOverflow: 'tooltip',
          // },
         
          {
            title: '当前状态',
            field: 'statusCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row }) => {
                const statusMap = {
                  0: '审批中',
                  1: '下发中',
                  2: '已驳回',
                  3: '已完成'
                }
                return statusMap[row.statusCode] || '-'
              }
            }
          },
          {
            title: '操作',
            field: 'operate',
            width: 200,
            align: 'center',
            slots: {
              default: ({ row, rowIndex }) => {
                const buttons = []
                
                // 状态按钮
                if (row.statusCode === 0) {
                  buttons.push(<a onClick={() => this.handleAudit(row)}>审核</a>)
                } else if (row.statusCode === 3) {
                  buttons.push(<a onClick={() => this.handleView(row)}>查看</a>)
                } else if (row.statusCode === 1) {
                  buttons.push(<a onClick={() => this.handleReceive(row)}>接收</a>)
                }
                
                // 编辑按钮（只在已驳回和审批中状态显示）
                if (row.statusCode === 2 || row.statusCode === 0) {
                  if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                  buttons.push(<a onClick={() => this.handleEdit(row)}>修改</a>)
                }
                
                // 复制按钮（固定显示）
                if (buttons.length > 0) buttons.push(<a-divider type='vertical' />)
                buttons.push(<a onClick={() => this.handleCopy(row)}>复制</a>)
                
                // 删除按钮（固定显示）
                buttons.push(<a-divider type='vertical' />)
                buttons.push(<a onClick={() => this.handleDelete(row)}>删除</a>)
                
                return <span>{buttons}</span>
              },
            },
          },
        ],
      }
    },
    created() {
      this.getList()
      getDispatchPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        if (res.code == 200) {
          this.dispatchOptions = res?.data?.data?.map(el => ({
            label: el.dispatchCode,
            value: el.dispatchId,
          }))
        }
      })
    },
    mounted() {},
    methods: {
      openAttach(row) {
        window.open(row.attachUrl)
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.startDate = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') + ' 00:00:00' : null
        this.queryParam.endDate = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') + ' 23:59:59' : null
      },

      calendarChange(dates) {
        if (dates?.length == 1) {
          this.disabledDealDate = dates[0]
        }
      },

      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showForm = false
        this.showFormDetails = false
        this.loading = true
        this.selectChange({ records: [] })
        getPage(this.queryParam).then(response => {
          this.list = response?.data?.data || []
          this.total = response?.data?.total
          this.loading = false
        })
      },
      //
      filterOption(input, option) {
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      },
      // 多选框选中
      selectChange(valObj) {
        this.selectIds = valObj.records.map(item => item.runCmdId)
        this.names = valObj.records.map(item => item.dispatchCode)
        this.isChecked = !!valObj.records.length
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          dispatchId: null,
          cmdName: '',
          cmdCode: '',
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 导出
      handleExport() {},
      /* 新增 */
      handleAdd() {
        this.showForm = true
        let row = { action: '新增' }
        this.$nextTick(() => this.$refs.formRef.handle(row))
      },
      /* 修改 */
      handleEdit(record) {
        this.showForm = true
        record.action = '修改'
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      handleCopy(record) {
        this.showForm = true
        record.action = '复制'
        this.$nextTick(() => this.$refs.formRef.handle(record))
      },
      handleDetail(record) {
        this.showDetail = true
        this.$nextTick(() => this.$refs.detailRef.handle(record))
      },

      handleAudit(record) {
        this.showDetail = true
        this.$nextTick(() => this.$refs.detailRef.handle(record, 'audit'))
      },

      handleView(record) {
        this.showDetail = true
        this.$nextTick(() => this.$refs.detailRef.handle(record, 'view'))
      },

      handleReceive(record) {
        this.showDetail = true
        this.$nextTick(() => this.$refs.detailRef.handle(record, 'receive'))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const ids = row?.runCmdId ? [row?.runCmdId] : this.selectIds
        this.$confirm({
          title: '确认删除所选中数据?',
          content: '请确认是否删除当前选中的数据',
          onOk() {
            deleteRunmd({ runCmdIds: ids.join(',') }).then(res => {
              if (res.code == 200) {
                that.$message.success(`成功删除 ${res.data} 条数据`, 3)
                that.selectChange({ records: [] })
                that.onOperationComplete()
              }
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },

      // 处理审核结果
      handleAuditResult(row, auditResult) {
        // 如果审核通过，状态变为下发中(1)，否则变为已驳回(2)
        this.getList()
      },

      // 处理接收操作
      handleReceiveResult(row) {
        // 接收后状态变为已完成(3)
        this.getList()
      },
    },
    computed: {
      hasAddPermission() {
        // 从用户信息中获取角色ID数组
        const userInfo = this.$store.getters.userInfo || {}
        const userRoles = userInfo.roleIds || []
        return userRoles.includes(10000) || userRoles.includes(10039)
      }
    },
  }
</script>
<style lang="less" scoped></style>


