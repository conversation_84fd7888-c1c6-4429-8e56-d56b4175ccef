import storage from 'store'
import { login, getUserProfile, logout, ssoLogin } from '@/api/login'
import { loginOrgList, switchLoginOrg, getUserInfo } from '@/api/common'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Cookies from 'js-cookie'
import { handleIcoCreate, setDocumentTitle } from '@/utils/setTtitleAndIco.js'

const user = {
  state: {
    user: {},
    loginOrgId: '',
    userId: '',
    token: '',
    name: '',
    userType: '',
    welcome: '',
    avatar: '',
    roles: [],
    portalConfigs: [],
    defaultPortal: {},
    info: {},
    platformVersion: '',
    orgList: Cookies.get('orgList') ? JSON.parse(Cookies.get('orgList')) : [],
    loginOrgName: Cookies.get('loginOrgName') || '',
    sysNoticeList: [],
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_INFO: (state, info) => {
      state.info = info
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_USER_TYPE: (state, userType) => {
      state.userType = userType
    },
    SET_PORTAL_CONFIG: (state, portalConfigs) => {
      state.portalConfigs = portalConfigs
    },
    SET_DEFAULT_PORTAL: (state, defaultPortal) => {
      state.defaultPortal = defaultPortal
    },
    SET_PLATFORM_VERSION: (state, platformVersion) => {
      state.platformVersion = platformVersion
    },
    SET_NOTICE_LIST: (state, sysNoticeList) => {
      state.sysNoticeList = sysNoticeList
    },
    SET_LOGINORGNAME: (state, loginOrgName) => {
      state.loginOrgName = loginOrgName
    },
    SET_ORG_LIST: (state, orgList) => {
      state.orgList = orgList
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      // 3.调用单点登录接口
      return new Promise((resolve, reject) => {
        ssoLogin(userInfo)
          .then(res => {
            // 4.获取用户信息  刷新token存到缓存中
            user.state.token = res.data.token
            user.state.info = res.data.user
            user.state.userId = res.data.user.userId
            storage.set('userId', res.data.user.userId)
            storage.set(ACCESS_TOKEN, res.data.token, 7 * 24 * 60 * 60 * 1000)
            commit('SET_TOKEN', res.data.token)

            getUserInfo().then(userRes => {
              // userDeptId = !Cookies.get('loginOrgId') ? user.data.loginOrgId : Cookies.get('loginOrgId')
              Cookies.set('loginOrgName', userRes.data.loginOrgName)
              Cookies.set('loginOrgId', userRes.data.loginOrgId)
              user.state.loginOrgName = userRes.data.loginOrgName

              setDocumentTitle(userRes.data.appName)

              loginOrgList().then(res => {
                user.state.orgList = res.data
                user.state.orgList.forEach(item => {
                  if (item.deptId == userRes.data.loginOrgId) {
                    item.disabled = true
                  } else {
                    item.disabled = false
                  }
                })
                Cookies.set('orgList', JSON.stringify(user.state.orgList))
              })
            })

            resolve()
          })
          .catch(error => {
            reject(error)
          })
          .finally(() => {})
      })
    },

    // 获取用户信息
    GetInfo({ commit }) {
      return new Promise((resolve, reject) => {
        let cas_userId = storage.get('userId')
        getUserProfile()
          .then(res => {
            // user.userType = 1;

            let userType = 1
            let resRoles = ['admin'] // res.roles=["admin"]
            let resPermissions = ['*:*:*'] //res.permissions
            let resUserPortalConfig = [
              {
                createByName: null,
                createDeptName: null,
                importErrInfo: null,
                id: '73c217ba0fb24945a8faef74eb10d302',
                searchValue: null,
                createBy: null,
                createDept: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                updateIp: null,
                remark: null,
                version: null,
                delFlag: '0',
                handleType: null,
                params: {},
                name: '首页',
                code: '6c297eb4651940edbb45c87c75be00d7',
                applicationRange: 'U',
                isDefault: 'Y',
                resourceId: '1',
                resourceName: null,
                systemDefinedId: 'app1',
                content: '',
                sort: null,
                saveType: null,
                status: '0',
                recordLog: true,
              },
            ] //res.userPortalConfig
            let resDefaultPortalConfig = {
              createByName: null,
              createDeptName: null,
              importErrInfo: null,
              id: '73c217ba0fb24945a8faef74eb10d302',
              searchValue: null,
              createBy: null,
              createDept: null,
              createTime: null,
              updateBy: null,
              updateTime: null,
              updateIp: null,
              remark: null,
              version: null,
              delFlag: '0',
              handleType: null,
              params: {},
              name: '首页',
              code: '6c297eb4651940edbb45c87c75be00d7',
              applicationRange: 'U',
              isDefault: 'Y',
              resourceId: '1',
              resourceName: null,
              systemDefinedId: 'app1',
              content: '',
              sort: null,
              saveType: null,
              status: '0',
              recordLog: true,
            } //res.defaultPortalConfig
            let resLincenseInfo = null //res.lincenseInfo
            let resSysNoticeList = [] //res.sysNoticeList
            const user = res.data //
            const avatar = user.avatar == '' || user.avatar == null ? '' : user.avatar
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', resRoles) //res.roles
              commit('SET_PERMISSIONS', resPermissions) //res.permissions
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT']) //
            }
            commit('SET_PORTAL_CONFIG', resUserPortalConfig) //res.userPortalConfig
            commit('SET_DEFAULT_PORTAL', resDefaultPortalConfig) //res.defaultPortalConfig
            commit('SET_NAME', user.name)
            commit('SET_AVATAR', avatar)
            commit('SET_USER_TYPE', userType) //user.userType
            commit('SET_PLATFORM_VERSION', resLincenseInfo) //res.lincenseInfo
            commit('SET_NOTICE_LIST', resSysNoticeList) //res.sysNoticeList
            commit('SET_INFO', user) // 设置用户信息到store
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 登出 同时注销CAS
    Logout({ commit }) {
      return new Promise(resolve => {
        logout()
          .then(res => {
            commit('SET_TOKEN', '')
            commit('SET_ROLES', [])
            commit('SET_PERMISSIONS', [])
            storage.remove(ACCESS_TOKEN)
            Cookies.remove('serveErr')
            storage.remove('userId')
            window.location.href = `${process.env.VUE_APP_CAS_URL}/logout?service=${window.location.href}`
            resolve()
          })
          .catch(() => {
            resolve()
          })
          .finally(() => {})
      })
    },

    //用户切换机构进行登录
    switchDept({ commit }, item) {
      //切换登录机构 switchLoginOrg({orgId:})
      return new Promise((resolve, reject) => {
        switchLoginOrg({ orgId: String(item.item.deptId) })
          .then(orgRes => {
            let orgArr = []

            storage.remove(ACCESS_TOKEN)
            Cookies.remove('serveErr')
            storage.remove('userId')
            Cookies.set('loginOrgName', orgRes.data.loginOrgName)
            Cookies.set('loginOrgId', orgRes.data.loginOrgId)
            user.state.token = orgRes.data.token
            user.state.info = orgRes.data.user
            user.state.userId = orgRes.data.user.userId
            storage.set('userId', orgRes.data.user.userId)
            storage.set(ACCESS_TOKEN, orgRes.data.token, 7 * 24 * 60 * 60 * 1000)
            Cookies.set('ACCESS_TOKEN', orgRes.data.token)
            commit('SET_TOKEN', orgRes.data.token)
            user.state.loginOrgId = orgRes.data.loginOrgId
            user.state.loginOrgName = orgRes.data.loginOrgName
            loginOrgList().then(res => {
              orgArr = res?.data
              orgArr.forEach(item => {
                if (item.deptId == (orgRes.data.loginOrgId || Cookies.set('loginOrgId'))) {
                  item.disabled = true
                } else {
                  item.disabled = false
                }
              })
              user.state.orgList = orgArr
              Cookies.set('orgList', JSON.stringify(user.state.orgList))
            })

            resolve()
            // window.location.href = `${process.env.VUE_APP_CAS_URL}/logout?service=${window.location.origin}`
            // window.location.href = `${process.env.VUE_APP_CAS_URL}/login?service=${window.location.origin}`
          })
          .catch(error => {
            reject(error)
          })
          .finally(() => {
            window.location.href = `${window.location.origin}`
          })
      })
    },
    //获取当前用户登录机构列表
    getOrgList() {},
  },
}

export default user
