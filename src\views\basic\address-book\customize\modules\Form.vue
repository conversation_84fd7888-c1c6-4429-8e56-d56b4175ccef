<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="700"
    @cancel="cancel"
    modalHeight="620"
  >
    <div slot="content">
      <!-- :label-col="labelCol" :wrapper-col="wrapperCol" -->
      <a-form-model ref="form" :model="form" :rules="rules">
        <a-row class="form-row" :gutter="32">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="姓名" prop="name">
              <a-input allowClear v-model="form.name" :maxLength="20" placeholder="请输入" prop="templateContent" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="手机号">
              <a-input-number style="width: 100%" v-model="form.mobile" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <!-- <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="反馈情况" prop="feedbackConcent">
              <a-textarea :row="6" v-model="form.feedbackConcent" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col> -->
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="邮件">
              <a-input v-model="form.email" placeholder="请输入" allow-clear />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="性别">
              <a-select placeholder="请选择性别" v-model="form.gender" style="width: 100%" allow-clear>
                <a-select-option v-for="(d, index) in sexOptions" :key="index" :value="d.dictKey">
                  {{ d.dictValue }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="所属分类">
              <a-tree-select
                v-model="form.categoryIds"
                :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                :tree-data="categoryTree"
                :replaceFields="{
                  children: 'children',
                  title: 'categoryName',
                  key: 'categoryId',
                  value: 'categoryId',
                }"
                tree-default-expand-all
                tree-checkable
                search-placeholder="请选择"
              ></a-tree-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getBaseCategory, getOptions } from '@/api/common'
  import { addContact } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'

  export default {
    name: 'Form',
    components: { AntModal },
    props: ['sexOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        labelCol: { span: 7 },
        wrapperCol: { span: 16 },
        // sexOptions: [
        //   { dictKey: '1', dictValue: '男' },
        //   { dictKey: '2', dictValue: '女' }
        // ],
        categoryTree: [],
        formTitle: '',
        contactId: null,
        form: {
          categoryIds: [],
          email: '',
          gender: '',
          mobile: '',
          name: '',
          remark: '',
          userId: null,
          // msgTplName: '',
          // templateContent: null
        },
        open: false,
        rules: {
          name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
          // templateContent: [{ required: true, message: '模板内容不能为空', trigger: 'blur' }]
        },
      }
    },
    created() {
      // 分类多选树
      getBaseCategory({ categoryType: 'structureCode' }).then(res => {
        this.categoryTree = res?.data
      })
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      handle(type, id) {
        this.open = true
        if (type == 'add') {
          this.formTitle = '新增'
        } else if (type == 'edit') {
          this.modalLoading = true
          this.formTitle = '修改'
          // this.form.contactId = id
          getContactDetail({ contactId: id }).then(res => {
            this.form = res.data
            this.modalLoading = false
          })
        }
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            if (this.form.contactId == null) {
              addContact(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('新增成功', 3)
                    this.open = false
                    this.$emit('ok')
                    this.$emit('close')
                  }
                })
                .catch(() => (this.loading = false))
            } else {
              this.form.categoryName = undefined
              editContact(this.form)
                .then(res => {
                  if (res.code == 200) {
                    this.$message.success('修改成功', 3)
                    this.open = false
                    this.$emit('ok')
                    this.$emit('close')
                  }
                })
                .catch(() => (this.loading = false))
            }
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  .min-input {
    width: 150px !important;
    margin-right: 8px;
  }
</style>
